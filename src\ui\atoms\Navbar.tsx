"use client";
import { useEffect, useState } from "react";
import { AppBar, Box, Button, Menu, MenuItem, Tab, Tabs, Typography } from "@mui/material";
import Image from "next/image";
import StyledLink from "../atoms/StyledLink";
import { ExpandMoreIcon } from "../atoms/ExpandMoreIcon";
import Icon from './Icon';

type NavbarProps = {
  firstName: string;
  redirect: string;
};

export default function Navbar({ firstName, redirect }: NavbarProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [tab, setTab] = useState<number>(-1);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    if (newValue === 0) {
      location.href = '/';
    } else if (newValue === 1) {
      location.href = '/marketplace';
    } else if (newValue === 2) {
      location.href = '/settings';
    }
  };

  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const MENU_ITEMS = [
    {
      href: "/settings",
      icon: <Icon name="avatar"/>,
      text: "Account Details",
    },
    {
      href: "/help",
      icon: <Icon name="help"/>,
      text: "Help",
      redirect: () => {
        window.open('https://tlphc.zendesk.com/hc/en-us/requests/new', '_blank');
      },
    },
    {
      href: `/`,
      icon: <Icon name="logout"/>,
      text: "Logout",
    },
  ];

  useEffect(() => {
    const path = window.location.pathname;
    console.log('>>>>', path);
    if (path === '/') {
      setTab(0);
    } else if (path === '/marketplace') {
      setTab(1);
    } else if (path === '/settings') {
      setTab(2);
    }
  }, []);

  return (
    <AppBar className="sticky top-0 left-0 right-0 flex items-center flex-row justify-between h-16 px-5 shadow-none bg-white border-0 border-b-2 border-solid border-border-primary">
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <StyledLink href="/">
          <Image src="/TLPLogo.svg" alt="TLP logo" width={142} height={24} />
        </StyledLink>
        <Tabs style={{ marginLeft: 30 }} value={tab} onChange={handleTabChange} centered>
          <Tab style={{ padding: 0, marginRight: 30, height: 64 }} label="Dashboard" />
          <Tab style={{ padding: 0, marginRight: 30, height: 64 }} label="Marketplace" />
          <Tab style={{ padding: 0, height: 64 }} label="Settings" />
        </Tabs>
      </Box>
      <Button
        id="basic-button"
        aria-controls={open ? "basic-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
        size="large"
        className="w-24 text-text-primary hover:text-primary-main"
        sx={{
          "& .MuiButton-endIcon": { marginLeft: 0 },
        }}
        endIcon={<ExpandMoreIcon />}
      >
        {firstName}
      </Button>

      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        {MENU_ITEMS.map((item) => (
          <StyledLink href={item.href} key={item.href} onRedirect={item.redirect}>
            <MenuItem className="gap-x-2.5 group w-52">
              {item?.icon}
              <Typography className="text-text-secondary group-hover:text-primary-main">
                {item.text}
              </Typography>
            </MenuItem>
          </StyledLink>
        ))}
      </Menu>
    </AppBar>
  );
}
