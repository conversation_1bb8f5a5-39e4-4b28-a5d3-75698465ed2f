export const API_ENDPOINTS = {
  LOGIN: '/api/authcenter/login',
  LOGOUT: '/api/authcenter/logout',
  GET_AUTH_CODE: '/api/authcenter/authorized-code',
  SEND_2FA_VERIFICATION_CODE: '/api/authcenter/mfa/send',
  VERIFY_2FA_CODE: '/api/authcenter/mfa/check',
  RESET_PASSWORD: '/api/authcenter/forget/password/reset',
  SEND_RESET_PASSWORD_EMAIL: '/api/authcenter/forget/password/send-code',
  VERIFY_RESET_PASSWORD_TOKEN: '/api/authcenter/forget/password/verify-code',
  REGISTER: '/api/user-account/register-uk',
  SEND_REGISTER_VERIFICATION_CODE: '/api/user-account/send-verification-code',
  CREATE_CREDENTIAL_VIA_CODE: '/api/user-account/callback',
  CHECK_LOGIN: '/api/user-account/check-login'
};

export type API_ENDPOINTS_KEYS = keyof typeof API_ENDPOINTS;

export const API_ENDPOINTS_ENUM: Record<API_ENDPOINTS_KEYS, API_ENDPOINTS_KEYS> = Object.fromEntries(
  Object.keys(API_ENDPOINTS).map(key => [key, key])
) as Record<API_ENDPOINTS_KEYS, API_ENDPOINTS_KEYS>;

