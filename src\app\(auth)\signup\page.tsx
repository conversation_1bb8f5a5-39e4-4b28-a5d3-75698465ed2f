"use client";

import * as yup from "yup";
import { useFormik } from "formik";
import Checkbox from "@/ui/atoms/Checkbox";
import Input from "@/ui/atoms/Input";
import { PhoneInput } from "@/ui/atoms";
import { FormControlLabel, Typo<PERSON>, Button } from "@mui/material";
import { useSearchParams } from "next/navigation";
import ConfirmSignUpForm from "@/ui/auth/confirm-signup-form";
import { useSnackbar } from "notistack";
import { register, sendRegisterVerificationCode } from "@/lib/api";
import { useCallback, useState, Suspense } from "react";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import md5 from "crypto-js/md5";
import { SignupFormStep } from "./types"; // Import the enum from the new file
import UserProfileMetaSection, {
  IUserProfileMetaValues,
} from "@/ui/UserProfileMetaSection";

const validationSchema = yup.object({
  password: yup
    .string()
    .min(
      9,
      "The password must be between 9-20 characters, have both numbers and letters, and no spaces."
    )
    .max(
      20,
      "The password must be between 9-20 characters, have both numbers and letters, and no spaces."
    )
    .matches(
      /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d\-/:;()$&@\".,?!'\[\]{}#%^*+=_\\|~<>€£¥·]{9,20}$/,
      "The password must be between 9-20 characters, have both numbers and letters, and no spaces."
    )
    .required(
      "The password must be between 9-20 characters, have both numbers and letters, and no spaces."
    ),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "Passwords do not match")
    .required("Confirm Password is required"),
  fname: yup.string().required("First Name is required"),
  sname: yup.string().required("Last Name is required"),
  email: yup
    .string()
    .required("Email is required")
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      "Email is invalid"
    ),
  phoneNumber: yup
    .string()
    .matches(/^\d*$/, "Phone number must contain only digits")
    .max(20, "Phone number must be less than or equal to 20 digits")
    .required("Phone Number is required"),
  phoneCode: yup.string().required("Country code is required"),
  phoneCountry: yup.string().required("Country is required"),
  agreement: yup.bool().oneOf([true], "You must agree to the terms"),
});

export type ValidationSchema = yup.InferType<typeof validationSchema>;

interface ISignUpValues {
  fname: string;
  sname: string;
  email: string;
  phoneNumber: string;
  phoneCode: string;
  phoneCountry: string;
  password: string;
  confirmPassword: string;
  software?: string;
  companyTradeName?: string;
  interested: string[];
  agreement: boolean;
  subscribed: boolean;
}

// Removed enum definition from here

const Container = () => {
  const initialValues: ISignUpValues = {
    fname: "",
    sname: "",
    email: "",
    phoneNumber: "",
    phoneCode: "",
    phoneCountry: "GB",
    password: "",
    confirmPassword: "",
    software: "",
    companyTradeName: "",
    interested: [],
    agreement: false,
    subscribed: false,
  };

  const searchParams = useSearchParams();
  const { enqueueSnackbar } = useSnackbar();
  const [step, setStep] = useState<SignupFormStep>(SignupFormStep.SIGN_UP);
  const [loading, setLoading] = useState(false);
  const [userProfileMeta, setUserProfileMeta] =
    useState<IUserProfileMetaValues | null>(null);

  const {
    values,
    handleBlur,
    handleChange,
    errors,
    touched,
    setFieldValue,
    handleSubmit,
  } = useFormik({
    onSubmit: onSubmit,
    validationSchema: validationSchema,
    enableReinitialize: true,
    validateOnBlur: true,
    initialValues: initialValues,
  });

  // 包装handleChange，使email输入自动转小写
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<any>) => {
      setFieldValue("email", e.target.value.toLowerCase());
    },
    [setFieldValue]
  );

  async function onSubmit(values: ISignUpValues) {
    // Proceed to the next step: User profile confirmation
    setStep(SignupFormStep.CONFIRM_USER_PROFILE_META);
  }

  const sendVerificationCode = useCallback(async () => {
    if (loading) {
      return;
    }
    setLoading(true);
    await sendRegisterVerificationCode({
      account: values.email,
      type: "REGISTER",
    });
    setLoading(false);
    enqueueSnackbar(
      "Verification code has been sent to your email, please check."
    );
  }, [enqueueSnackbar, loading, values.email]);

  const handleUserProfileMetaUpdate = useCallback(
    (UserProfileMeta: IUserProfileMetaValues) => {
      Object.keys(UserProfileMeta).forEach((key) => {
        setFieldValue(
          key,
          UserProfileMeta[key as keyof IUserProfileMetaValues]
        );
      });
    },
    [setFieldValue]
  );

  const handleConfirmUserProfileMeta = useCallback(async () => {
    setLoading(true);
    try {
      // Send registration verification code
      await sendVerificationCode();

      // Enter verification code confirmation step
      setStep(SignupFormStep.CONFIRM_VERIFICATION_CODE);
    } catch (error: any) {
      console.error("Failed to send verification code:", error);
      enqueueSnackbar(
        error?.message || "Failed to send verification code, please try again."
      );
    } finally {
      setLoading(false);
    }
  }, [sendVerificationCode, enqueueSnackbar]);

  const onSignup = useCallback(
    async (code: string) => {
      // Added code parameter
      setLoading(true);
      try {
        const formValues = {
          firstName: values.fname,
          lastName: values.sname,
          account: values.email,
          password: md5(values.password).toString(),
          phoneNumber: values.phoneNumber,
          phoneCode: values.phoneCode,
          phoneCountry: values.phoneCountry,
          verificationCode: code, // Use code parameter
          source: "portal",
          busiInterest: values.interested,
          companyTradeName: values.companyTradeName,
          softwareUsed: values.software,
          isSubscribed: values.subscribed,
          isAgreed: values.agreement,
        };

        const resp = await register(formValues);
        if (resp?.status?.code === 0) {
          enqueueSnackbar(
            "Registration successful! Redirecting to login page..."
          );
          setTimeout(() => {
            if (searchParams.get("redirect")) {
              location.href = `/?redirect=${searchParams.get("redirect")}`;
            } else {
              location.href = "/";
            }
          }, 1500);
        } else {
          enqueueSnackbar(
            resp?.status?.msg ||
              "Registration failed, please check if the verification code is correct."
          );
        }
      } catch (error: any) {
        console.error(`Registration failed: ${JSON.stringify(error)}`);
        enqueueSnackbar(
          error?.message ||
            "Registration failed, please check if the verification code is correct."
        );
      } finally {
        setLoading(false);
      }
    },
    [
      values.fname,
      values.sname,
      values.email,
      values.password,
      values.phoneNumber,
      values.interested,
      values.companyTradeName,
      values.software,
      enqueueSnackbar,
      searchParams,
      values.subscribed,
      values.agreement,
      values.phoneCode,
      values.phoneCountry,
    ]
  );

  const onBack = useCallback(() => {
    switch (step) {
      case SignupFormStep.CONFIRM_USER_PROFILE_META: {
        setStep(SignupFormStep.SIGN_UP);
        break;
      }
      case SignupFormStep.CONFIRM_VERIFICATION_CODE: {
        setStep(SignupFormStep.CONFIRM_USER_PROFILE_META);
        break;
      }
      default: {
        setStep(SignupFormStep.SIGN_UP);
        break;
      }
    }
  }, [step]);

  return (
    <>
      {step !== SignupFormStep.SIGN_UP ? (
        <Button
          startIcon={
            <ArrowLeftIcon color="inherit" style={{ width: 14, height: 14 }} />
          }
          className="absolute top-4 left-4 text-[#515666] px-4"
          onClick={onBack}
          data-testid="signup-back-btn"
        >
          Back
        </Button>
      ) : null}
      <div className="flex justify-center m-[auto] w-[420px]">
        {step === SignupFormStep.SIGN_UP ? (
          <div className="w-full" data-testid="signup-form-root">
            <Typography
              variant="h2"
              component="h2"
              fontSize={24}
              fontWeight={700}
              data-testid="signup-title"
            >
              Sign Up
            </Typography>
            <form
              onSubmit={handleSubmit}
              className="flex flex-col mt-[30px]"
              data-testid="signup-form"
            >
              <div className="flex gap-2.5">
                <div className="flex-1">
                  <Input
                    label="First Name"
                    required
                    name="fname"
                    id="fname"
                    disabled={loading}
                    value={values.fname}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    fullWidth
                    placeholder="Enter First Name"
                    error={!!errors.fname && touched.fname}
                    helperText={
                      errors.fname && touched.fname ? errors.fname : " "
                    }
                    inputProps={{ "data-testid": "signup-fname" }}
                  />
                </div>
                <div className="flex-1">
                  <Input
                    label="Last Name"
                    required
                    name="sname"
                    id="sname"
                    disabled={loading}
                    value={values.sname}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    fullWidth
                    placeholder="Enter Last Name"
                    error={!!errors.sname && touched.sname}
                    helperText={
                      errors.sname && touched.sname ? errors.sname : " "
                    }
                    inputProps={{ "data-testid": "signup-sname" }}
                  />
                </div>
              </div>
              <PhoneInput
                label="Phone Number"
                required
                name="phoneNumber"
                id="phoneNumber"
                disabled={loading}
                value={values.phoneNumber}
                defaultCountry={values.phoneCountry}
                onChange={(phone, { code, dialCode }) => {
                  setFieldValue("phoneNumber", phone);
                  setFieldValue("phoneCode", dialCode);
                  setFieldValue("phoneCountry", code);
                }}
                onBlur={handleBlur}
                placeholder="Enter Number"
                error={!!errors.phoneNumber && touched.phoneNumber}
                helperText={
                  errors.phoneNumber && touched.phoneNumber
                    ? errors.phoneNumber
                    : " "
                }
                inputProps={{ "data-testid": "signup-phoneNumber" }}
              />
              <Input
                label="Email Address"
                required
                name="email"
                id="email"
                disabled={loading}
                value={values.email}
                onBlur={handleBlur}
                onChange={handleInputChange}
                placeholder="Enter Email"
                error={!!errors.email && touched.email}
                helperText={errors.email && touched.email ? errors.email : " "}
                inputProps={{ "data-testid": "signup-email" }}
              />
              <Input
                label="Password"
                required
                placeholder="Enter Password"
                name="password"
                type="password"
                id="password"
                disabled={loading}
                value={values.password}
                onBlur={handleBlur}
                onChange={handleChange}
                error={!!errors.password && touched.password}
                helperText={
                  errors.password && touched.password ? errors.password : " "
                }
                inputProps={{ "data-testid": "signup-password" }}
              />
              <Input
                label="Re-Enter Password"
                required
                placeholder="Enter Confirm Password"
                name="confirmPassword"
                type="password"
                id="confirmPassword"
                disabled={loading}
                value={values.confirmPassword}
                onBlur={handleBlur}
                onChange={handleChange}
                error={!!errors.confirmPassword && touched.confirmPassword}
                helperText={
                  errors.confirmPassword && touched.confirmPassword
                    ? errors.confirmPassword
                    : " "
                }
                inputProps={{ "data-testid": "signup-confirmPassword" }}
              />
              <div>
                <FormControlLabel
                  className="agreement-checkbox"
                  required
                  control={
                    <Checkbox
                      name="agreement"
                      color="primary"
                      checked={values.agreement}
                      onChange={handleChange}
                      inputProps={{ "data-testid": "signup-agreement" } as any}
                    />
                  }
                  label={
                    <span className="text-sm">
                      I agree to the{" "}
                      <a
                        target="_blank"
                        href="https://loftyworks.com/terms-and-conditions/"
                        className="text-primary-main no-underline"
                        data-testid="signup-terms-link"
                      >
                        Terms & Conditions
                      </a>{" "}
                      and{" "}
                      <a
                        href="https://loftyworks.com/privacy-policy/"
                        target="_blank"
                        className="text-primary-main no-underline"
                        data-testid="signup-privacy-link"
                      >
                        Privacy Policy
                      </a>
                    </span>
                  }
                  data-testid="signup-agreement-label"
                />
                <FormControlLabel
                  className="agreement-checkbox"
                  control={
                    <Checkbox
                      name="subscribed"
                      checked={values.subscribed}
                      onChange={handleChange}
                      inputProps={{ "data-testid": "signup-subscribed" } as any}
                    />
                  }
                  label={
                    <span className="text-sm">
                      Subscribe to our newsletter, unsubscribe any moment
                    </span>
                  }
                  data-testid="signup-subscribed-label"
                />
              </div>
              <Button
                variant="contained"
                type="submit"
                className="mt-[30px] mb-[30px] h-10"
                disabled={loading}
                data-testid="signup-next-btn"
              >
                Next
              </Button>
              <p className="text-sm text-center">
                <span className="text-[14px]">Already have an account? </span>
                <a
                  href={`/${
                    searchParams.get("redirect")
                      ? `?redirect=${searchParams.get("redirect")}`
                      : ""
                  }`}
                  className="text-primary-main no-underline"
                  data-testid="signup-login-link"
                >
                  Login
                </a>
              </p>
            </form>
          </div>
        ) : null}
        {step === SignupFormStep.CONFIRM_USER_PROFILE_META ? (
          <UserProfileMetaSection
            title="Business Details"
            isLoading={loading}
            onUpdate={handleUserProfileMetaUpdate}
            callback={handleConfirmUserProfileMeta}
            profileMetaValues={values as IUserProfileMetaValues}
          />
        ) : null}
        {step === SignupFormStep.CONFIRM_VERIFICATION_CODE ? (
          <ConfirmSignUpForm
            loading={loading}
            resendCode={sendVerificationCode}
            onVerify={onSignup} // Pass onSignup as onVerify callback
          />
        ) : null}
      </div>
    </>
  );
};

export default function SignUp() {
  return (
    <Suspense fallback={<p>Loading...</p>}>
      <Container />
    </Suspense>
  );
}
