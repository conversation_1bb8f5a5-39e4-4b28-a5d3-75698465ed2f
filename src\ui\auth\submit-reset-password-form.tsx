"use client";

import Input from "@/ui/atoms/Input";
import * as yup from "yup";

import { useFormik } from "formik";
import { CodeInput } from "@/ui/atoms/CodeInput";
import { useSnackbar } from "notistack";
import { resetPassword, sendResetPasswordEmail } from "@/lib/api";
import { ArrowPathIcon, ArrowLeftIcon } from "@heroicons/react/24/outline";
import Button from "@/ui/atoms/Button";
import "./style.css";

import { useCallback, useState } from "react";

interface IConfirmCodeValues {
  code: string;
  confirmPassword: string;
  password: string;
}

export default function SubmitResetPasswordFrom(props: any) {
  const { email: userEmail, setConfirmEmail } = props;
  const [loading, setLoading] = useState(false);

  const initialValues: IConfirmCodeValues = {
    code: "",
    confirmPassword: "",
    password: "",
  };

  const { enqueueSnackbar } = useSnackbar();

  const [isReadyToConfirmCode, setIsReadyToConfirmCode] = useState(false);

  const validationSchema = yup.object({
    password: yup
      .string()
      .min(
        9,
        "The password must be between 9-20 characters, have both numbers and letters, and no spaces."
      )
      .max(
        20,
        "The password must be between 9-20 characters, have both numbers and letters, and no spaces."
      )
      .matches(
        /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d\-/:;()$&@\".,?!'\[\]{}#%^*+=_\\|~<>€£¥·]{9,20}$/,
        "The password must be between 9-20 characters, have both numbers and letters, and no spaces."
      )
      .required(
        "The password must be between 9-20 characters, have both numbers and letters, and no spaces."
      ),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password")], "Password confirmation must match password")
      .required("Password is required"),
    code: yup.string().optional(),
  });

  const handleReset = useCallback(
    async (values: IConfirmCodeValues) => {
      console.log("value", JSON.stringify(values));
      if (!isReadyToConfirmCode) {
        enqueueSnackbar("Please enter the 6-digit verification code.");
        return;
      }
      setLoading(true);
      try {
        const resp = await resetPassword({
          account: userEmail,
          code: values.code,
          password: values.password,
        });
        console.log("resp", JSON.stringify(resp));
        if (resp?.httpStatus === 200 && resp.ok) {
          console.log("Successfully Reset Password.");
          enqueueSnackbar("Successfully Reset Password.");
          setConfirmEmail(2);
          return;
        }
        enqueueSnackbar(resp?.message || "Failed to reset password!");
      } catch (error: any) {
        console.error(`Error ResetPassword in ${JSON.stringify(error)}`);
        enqueueSnackbar(error?.message);
      } finally {
        setLoading(false);
      }
    },
    [enqueueSnackbar, isReadyToConfirmCode, setConfirmEmail, userEmail]
  );

  const resendCode = useCallback(async () => {
    if (loading) {
      return;
    }
    try {
      setLoading(true);
      const resp = await sendResetPasswordEmail({
        account: userEmail,
      });
      console.log("resp", JSON.stringify(resp));
      if (resp?.httpStatus === 200 && resp.ok) {
        enqueueSnackbar("Please check your email.");
        setLoading(false);
      } else {
        enqueueSnackbar(resp?.message || "Failed to send verify code");
      }
    } catch (error: any) {
      console.error(`Error SendVerifyCode in ${JSON.stringify(error)}`);
      enqueueSnackbar(error?.message);
    } finally {
      setLoading(false);
    }
  }, [enqueueSnackbar, loading, userEmail]);

  const {
    values,
    handleBlur,
    handleChange,
    errors,
    touched,
    setFieldValue,
    handleSubmit,
  } = useFormik({
    onSubmit: handleReset,
    validationSchema: validationSchema,
    enableReinitialize: true,
    validateOnBlur: true,
    initialValues: initialValues,
  });

  return (
    <form
      className="h-full"
      onSubmit={handleSubmit}
      data-testid="reset-password-form-root"
    >
      <div
        className="backItem flex items-center cursor-pointer absolute left-5 top-5"
        onClick={() => setConfirmEmail(0)}
        data-testid="reset-password-form-back"
      >
        <ArrowLeftIcon className="w-4 h-4" color="text-gray-300" />
        <span
          style={{
            fontWeight: 400,
            fontSize: 14,
            lineHeight: "20px",
            marginLeft: "8px",
            color: "#515666",
          }}
        >
          Back
        </span>
      </div>
      <div className="flex flex-col flex-1 justify-center h-full w-[420px] m-auto ">
        <h1
          className={`mb-3 text-2xl font-bold`}
          data-testid="reset-password-form-title"
        >
          Check Your Email
        </h1>
        <h4
          style={{
            fontWeight: 400,
            color: "#A0A3AF",
            marginBottom: "30px",
            fontSize: 14,
          }}
          data-testid="reset-password-form-desc"
        >
          We sent a code to{" "}
          <span
            style={{ fontWeight: 500, color: "#202437" }}
            data-testid="reset-password-form-email"
          >
            {userEmail || ""}
          </span>
        </h4>
        <CodeInput
          name="code"
          id="code"
          value={values.code}
          disabled={loading}
          onBlur={handleBlur}
          setIsReadyToConfirmCode={setIsReadyToConfirmCode}
          setFieldValue={setFieldValue}
          error={errors.code}
          dataTestIdPrefix="reset-password-form-code"
        />

        <div className="flex justify-center  mt-[30px] mb-[30px] text-sm text-center ">
          <div
            className="flex items-center gap-1 cursor-pointer"
            onClick={resendCode}
            data-testid="reset-password-form-resend"
          >
            <ArrowPathIcon className="w-4 h-4" color="text-gray-300" />
            <span className="text-primary-main">Resend code</span>
          </div>
        </div>

        <div
          style={{
            height: "1px",
            width: "100%",
            backgroundColor: "#EBECF1",
            marginBottom: "30px",
          }}
        />
        <div className="w-full">
          <Input
            label="New Password"
            required
            placeholder="Enter Password"
            name="password"
            type="password"
            id="password"
            disabled={loading}
            value={values.password}
            onBlur={handleBlur}
            onChange={handleChange}
            error={!!errors.password && touched.password}
            helperText={
              errors.password && touched.password ? errors.password : " "
            }
            inputProps={{ "data-testid": "reset-password-form-password" }}
          />

          <Input
            label="Confirm New Password"
            required
            placeholder="Enter Password"
            name="confirmPassword"
            type="password"
            id="confirmPassword"
            disabled={loading}
            value={values.confirmPassword}
            onBlur={handleBlur}
            onChange={handleChange}
            error={!!errors.confirmPassword && touched.confirmPassword}
            helperText={
              errors.confirmPassword && touched.confirmPassword
                ? errors.confirmPassword
                : " "
            }
            inputProps={{
              "data-testid": "reset-password-form-confirm-password",
            }}
          />

          <Button
            className="w-full h-[40px] font-normal"
            variant="contained"
            type="submit"
            disabled={loading}
            loading={loading}
            data-testid="reset-password-form-submit"
          >
            Reset Password
          </Button>
        </div>
      </div>
    </form>
  );
}
