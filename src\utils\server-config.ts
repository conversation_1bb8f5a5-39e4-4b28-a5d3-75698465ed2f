// Define configuration interface
interface EnvConfig {
  ENVIRONMENT: string;
  GOOGLE_RECAPTURE_KEY: string;
  LOFTY_AUTH_SERVE_BASE_URL: string;
  LOFTY_SALES_SERVE_BASE_URL: string;
  DEFAULT_LANDING_PAGE: string;
  LOFTY_AUTH_SERVE_CLIENT_ID: string;
  [key: string]: string | undefined;
}

// Development/Default Configuration
const devConfig: EnvConfig = {
  ENVIRONMENT: 'development',
  GOOGLE_RECAPTURE_KEY: '6LdJgo4jAAAAAHYoowU4gkLZFcbXUZxTRF0FAXg5',
  LOFTY_AUTH_SERVE_BASE_URL: 'https://auth.stage.eu.loftyworks.com',
  LOFTY_SALES_SERVE_BASE_URL: 'https://sales.stage.eu.loftyworks.com',
  DEFAULT_LANDING_PAGE: 'https://portal.stage.eu.loftyworks.com',
  LOFTY_AUTH_SERVE_CLIENT_ID: 'lofty',
};

// Stage Configuration
const stageConfig: EnvConfig = {
  ENVIRONMENT: 'stage',
  GOOGLE_RECAPTURE_KEY: '6LdJgo4jAAAAAHYoowU4gkLZFcbXUZxTRF0FAXg5',
  LOFTY_AUTH_SERVE_BASE_URL: 'https://auth.stage.eu.loftyworks.com',
  LOFTY_SALES_SERVE_BASE_URL: 'https://sales.stage.eu.loftyworks.com',
  DEFAULT_LANDING_PAGE: 'https://portal.stage.eu.loftyworks.com',
  LOFTY_AUTH_SERVE_CLIENT_ID: 'lofty',
};

// Production Configuration
const prodConfig: EnvConfig = {
  ENVIRONMENT: 'production',
  GOOGLE_RECAPTURE_KEY: '6LdJgo4jAAAAAHYoowU4gkLZFcbXUZxTRF0FAXg5',
  LOFTY_AUTH_SERVE_BASE_URL: 'https://auth.loftyworks.com',
  LOFTY_SALES_SERVE_BASE_URL: 'https://sales.loftyworks.com',
  DEFAULT_LANDING_PAGE: 'https://portal.loftyworks.com',
  LOFTY_AUTH_SERVE_CLIENT_ID: 'lofty',
};

/**
 * Get the corresponding environment configuration based on the hostname
 * @param hostname - The hostname from the request (e.g., 'localhost', 'app.stage.eu.loftyworks.com')
 * @returns The environment configuration object.
 */
export function getConfig(hostname: string): EnvConfig {
  if (process.env.NODE_ENV === 'development') {
    return devConfig;
  }
  if (hostname.endsWith('.stage.eu.loftyworks.com')) {
    return stageConfig;
  }
  if (hostname.endsWith('.loftyworks.com')) {
    return prodConfig;
  }
  // Default fallback to dev configuration
  return devConfig;
}
