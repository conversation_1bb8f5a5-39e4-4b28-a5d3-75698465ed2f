import React from "react";
import { Paper, Stack, Avatar, Box, Typography } from "@mui/material";
import { twMerge } from "tailwind-merge";
import { MfaVerifyCodeKey } from "@/lib/api";

interface AuthMethodCardProps {
  icon: React.ReactNode;
  title: string;
  description: React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
  dataMethod?: MfaVerifyCodeKey;
  className?: string;
}

const AuthMethodCard: React.FC<AuthMethodCardProps> = ({
  icon,
  title,
  description,
  onClick,
  dataMethod,
  className = "",
}) => {
  return (
    <Paper
      elevation={0}
      onClick={onClick}
      data-method={dataMethod}
      className={twMerge("p-4 border border-solid border-gray-300 rounded cursor-pointer hover:bg-gray-100")}
    >
      <Stack direction="row" spacing={2} alignItems="center">
        <Avatar className={twMerge("w-[60px] h-[60px] flex items-center justify-center", className)}>
          {icon}
        </Avatar>
        <Box>
          <Typography component="div" variant="body1" color="text.primary">
            {title}
          </Typography>
          <Typography component="div" variant="body2" color="text.secondary">
            {description}
          </Typography>
        </Box>
      </Stack>
    </Paper>
  );
};

export default AuthMethodCard; 