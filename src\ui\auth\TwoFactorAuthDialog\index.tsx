"use client";

import {
  <PERSON><PERSON>,
  DialogTitle,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Box,
  Stack,
  Link,
  Menu,
  MenuItem,
  useTheme,
} from "@mui/material";
import {
  XMarkIcon,
  EnvelopeIcon,
  PhoneArrowUpRightIcon,
  DevicePhoneMobileIcon,
  ChevronDownIcon,
  QrCodeIcon,
} from "@heroicons/react/24/outline";
import React, { useCallback, useEffect, useState, useMemo } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  verify2faCode,
  send2faVerificationCode,
  MfaVerifyCodeType,
  MfaVerifyCodeKeys,
  MfaVerifyCodeKey,
} from "@/lib/api";
import { useSnackbar } from "notistack";
import AuthMethodCard from "./AuthMethodCard";
import VerifyCodePanel from "./VerifyCodePanel";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";

type contactType = "PHONE" | "EMAIL" | "AUTHENTICATOR";

interface Contact {
  id: number;
  contactType: contactType;
  contactInfo: string;
}
export interface Accounts {
  phone: Contact[];
  email: Contact[];
  authenticator?: Contact[];
}

interface TwoFactorAuthDialogProps {
  accounts?: Accounts;
  open: boolean;
  onClose: () => void;
  sessionId: string;
  onSuccess?: (data: any) => void;
}

export default function TwoFactorAuthDialog({
  accounts,
  open,
  onClose,
  sessionId,
  onSuccess,
}: TwoFactorAuthDialogProps) {
  const [isVerifyCode, setIsVerifyCode] = useState(false);
  const [countDown, setCountDown] = useState(60);
  const [verifyMethod, setVerifyMethod] =
    useState<keyof typeof MfaVerifyCodeType>();
  const [account, setAccount] = useState<Contact[] | null>(null); // Stores the contact list for current dropdown menu
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedAccounts, setSelectedAccounts] = useState<{
    [key: string]: Contact;
  }>({});
  const theme = useTheme();
  const { executeRecaptcha } = useGoogleReCaptcha();

  useEffect(() => {
    if (accounts) {
      const initialSelected: Record<string, Contact> = {};
      Object.keys(accounts).forEach((key: string) => {
        const accountList = accounts[key as keyof Accounts]; // Store in a variable for clearer type inference
        if (accountList && accountList.length > 0) {
          const mfaTypeEnumKey = key.toUpperCase(); // Convert key to uppercase and use as enum key
          if (mfaTypeEnumKey === "PHONE") {
            initialSelected[MfaVerifyCodeKeys.CALL] = accountList[0]; // Store the contact info for each method
            initialSelected[MfaVerifyCodeKeys.SMS] = accountList[0]; // Store the contact info for each method
          } else {
            initialSelected[mfaTypeEnumKey] = accountList[0]; // Store the contact info for each method
          }
        }
      });
      setSelectedAccounts(
        initialSelected as { [key in MfaVerifyCodeType]?: Contact }
      );
    }
  }, [accounts]);

  const availableMethods = useMemo(
    () => Object.keys(selectedAccounts || {}),
    [selectedAccounts]
  );

  const handleEmailDropdown = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      event.stopPropagation();
      if (accounts?.email) {
        setAccount(accounts?.email);
        setAnchorEl(event.currentTarget);
        setVerifyMethod(MfaVerifyCodeKeys.EMAIL);
      }
    },
    [accounts?.email]
  );

  const handlePhoneDropdown = useCallback(
    (event: React.MouseEvent<HTMLDivElement>, methodType: MfaVerifyCodeKey) => {
      event.stopPropagation();

      if (accounts?.phone) {
        setAccount(accounts?.phone);
        setAnchorEl(event.currentTarget);
        setVerifyMethod(methodType);
      }
    },
    [accounts?.phone]
  );

  const handleCloseMenu = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const handleSelectAccount = useCallback(
    (contact: Contact) => {
      setSelectedAccounts((prev) => ({
        ...prev,
        [verifyMethod!]: contact,
      }));
      setAnchorEl(null);
    },
    [verifyMethod]
  );

  const { enqueueSnackbar } = useSnackbar();

  const {
    values,
    handleBlur,
    setFieldValue,
    setErrors,
    setSubmitting,
    errors,
    handleSubmit,
  } = useFormik({
    initialValues: {
      code: "",
    },
    validationSchema: Yup.object().shape({
      code: Yup.string()
        .required("Verification code is required")
        .length(6, "Code must be 6 digits"),
    }),
    onSubmit: onSubmit,
  });

  const handleClose = useCallback(() => {
    onClose?.();
    setIsVerifyCode(false);
    setFieldValue("code", "");
    setErrors({});
  }, [onClose, setFieldValue, setErrors]);

  async function onSubmit(values: { code: string }) {
    try {
      const account = selectedAccounts[verifyMethod!];
      if (!executeRecaptcha) return;
      const token = await executeRecaptcha("verify2faCode");

      const response = await verify2faCode({
        sessionId,
        accountId: `${account?.id}`,
        type: verifyMethod!,
        code: values.code,
        "g-recaptcha-response": token,
      });
      if (response.ok && response.status.code === 0) {
        enqueueSnackbar("Verification successful!");
        onSuccess?.(response.data.code);
        handleClose();
      } else {
        setErrors({
          code: response.message || "Invalid verification code",
        });
        enqueueSnackbar(response.message || "Invalid verification code");
      }
    } catch (error: any) {
      setErrors({ code: error.message || "An unexpected error occurred" });
      enqueueSnackbar(error.message || "An unexpected error occurred");
    } finally {
      setSubmitting(false);
    }
  }

  useEffect(() => {
    if (values.code.length === 6 && !errors.code) {
      handleSubmit();
    }
  }, [errors.code, handleSubmit, values]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isVerifyCode && countDown > 0) {
      timer = setInterval(() => {
        setCountDown((prev) => prev - 1);
      }, 1000);
    } else if (countDown === 0) {
      clearInterval(timer!);
    }
    return () => clearInterval(timer);
  }, [isVerifyCode, countDown]);

  const handleResendCode = useCallback(
    async (method?: MfaVerifyCodeKey) => {
      try {
        if (!executeRecaptcha) return;
        const type = method || verifyMethod || "EMAIL";
        const account = selectedAccounts[type!];
        const token = await executeRecaptcha("send2faVerificationCode");
        const response = await send2faVerificationCode({
          sessionId,
          accountId: `${account?.id}`,
          type: type, // 使用选择的验证方法
          "g-recaptcha-response": token,
        });
        if (response.ok && response.status.code === 0) {
          setCountDown(60); // Reset countdown
          enqueueSnackbar("Verification code sent!", { variant: "success" });
          setErrors({ code: "" }); // Clear any previous errors
        } else {
          enqueueSnackbar(response.message || "Failed to resend code");
        }
      } catch (error: any) {
        enqueueSnackbar(error.message || "An unexpected error occurred");
      }
    },
    [
      executeRecaptcha,
      verifyMethod,
      selectedAccounts,
      sessionId,
      enqueueSnackbar,
      setErrors,
    ]
  );

  const handleBack = useCallback(() => {
    setIsVerifyCode(false);
    setFieldValue("code", "");
    setErrors({});
  }, [setFieldValue, setErrors]);

  const handleClickVerifyMethod = useCallback(
    async (e: React.MouseEvent<HTMLDivElement>) => {
      e.stopPropagation();
      const method = (e.currentTarget.dataset["method"] ||
        "EMAIL") as MfaVerifyCodeKey;
      setVerifyMethod(method);
      // send verify code
      await handleResendCode(method);
      setCountDown(60);
      setIsVerifyCode(true);
    },
    [handleResendCode]
  );

  // 处理authenticator点击
  const handleClickAuthenticator = useCallback(() => {
    setVerifyMethod(MfaVerifyCodeKeys.AUTHENTICATOR);
    setIsVerifyCode(true);
  }, []);

  // 认证方式配置项
  const authMethodConfigs = useMemo(
    () =>
      [
        {
          icon: <EnvelopeIcon className="w-6 h-6" />,
          title: "Email verification",
          className: "bg-[#E7F6FF] text-[#2492FC]",
          description: (
            <>
              A 6-digit code was sent to
              <Box
                onClick={handleEmailDropdown}
                className="text-primary inline-flex items-center cursor-pointer ml-2"
                style={{ color: theme.palette.primary.main }}
              >
                {selectedAccounts?.EMAIL?.contactInfo || "N/A"}
                <ChevronDownIcon
                  className={`w-4 h-4 transition-transform duration-300 ease-in-out ${
                    Boolean(anchorEl) &&
                    verifyMethod === MfaVerifyCodeKeys.EMAIL
                      ? "rotate-180"
                      : "rotate-0"
                  }`}
                />
              </Box>
              The code expires in 10 minutes.
            </>
          ),
          onClick: handleClickVerifyMethod,
          dataMethod: MfaVerifyCodeKeys.EMAIL,
        },
        {
          icon: <DevicePhoneMobileIcon className="w-6 h-6" />,
          title: "Phone number verification",
          className: "text-[#3B5CDE] bg-[#EBEFFC]",
          description: (
            <>
              A 6-digit code was sent to
              <Box
                onClick={(e) => {
                  e.stopPropagation();
                  handlePhoneDropdown(
                    e as React.MouseEvent<HTMLDivElement>,
                    MfaVerifyCodeKeys.SMS
                  );
                }}
                style={{ color: theme.palette.primary.main }}
                className="text-primary inline-flex items-center cursor-pointer ml-2"
              >
                {selectedAccounts?.SMS?.contactInfo || "N/A"}
                <ChevronDownIcon
                  className={`w-4 h-4 transition-transform duration-300 ease-in-out ${
                    Boolean(anchorEl) && verifyMethod === MfaVerifyCodeKeys.SMS
                      ? "rotate-180"
                      : "rotate-0"
                  }`}
                />
              </Box>
              The code expires in 10 minutes.
            </>
          ),
          onClick: handleClickVerifyMethod,
          dataMethod: MfaVerifyCodeKeys.SMS,
        },
        {
          icon: <PhoneArrowUpRightIcon className="w-6 h-6" />,
          title: "Telephone voice",
          className: "bg-[#FEF8E4] text-[#FFA600]",
          description: (
            <>
              A 6-digit code was sent to
              <Box
                onClick={(e) => {
                  e.stopPropagation();
                  handlePhoneDropdown(
                    e as React.MouseEvent<HTMLDivElement>,
                    MfaVerifyCodeKeys.CALL
                  );
                }}
                style={{ color: theme.palette.primary.main }}
                className="text-primary inline-flex items-center cursor-pointer ml-2"
              >
                {selectedAccounts?.CALL?.contactInfo || "N/A"}
                <ChevronDownIcon
                  className={`w-4 h-4 transition-transform duration-300 ease-in-out ${
                    Boolean(anchorEl) && verifyMethod === MfaVerifyCodeKeys.CALL
                      ? "rotate-180"
                      : "rotate-0"
                  }`}
                />
              </Box>
              The code expires in 10 minutes.
            </>
          ),
          onClick: handleClickVerifyMethod,
          dataMethod: MfaVerifyCodeKeys.CALL,
        },
        {
          icon: <QrCodeIcon className="w-6 h-6" />,
          title: "Authenticator",
          className: "bg-[#E7FFE3] text-[#19AB63]",
          description: (
            <>
              Get a verification code from the{" "}
              <span className="font-bold">Authenticator</span> app
            </>
          ),
          onClick: handleClickAuthenticator,
          dataMethod: MfaVerifyCodeKeys.AUTHENTICATOR,
        },
      ].filter((item) => availableMethods.includes(item.dataMethod)),
    [
      handleEmailDropdown,
      theme.palette.primary.main,
      selectedAccounts,
      anchorEl,
      verifyMethod,
      handleClickVerifyMethod,
      handleClickAuthenticator,
      handlePhoneDropdown,
      availableMethods,
    ]
  );

  return (
    <Dialog
      open={!!open}
      onClose={handleClose}
      PaperProps={{ className: "w-[480px] max-w-[480px]" }}
    >
      <DialogTitle className="flex justify-between items-center px-[30px] py-[13px] relative">
        <Typography
          variant="body1"
          color="text.primary"
          className="text-[#202437] font-bold text-base leading-6"
        >
          TWO-FACTOR AUTHENTICATION
        </Typography>
        <IconButton
          onClick={handleClose}
          aria-label="close"
          color="inherit"
          className="absolute right-5 top-2"
        >
          <XMarkIcon className="w-6 h-6" />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers className="px-[30px] py-5 no-scrollbar">
        {isVerifyCode ? (
          <VerifyCodePanel
            contactInfo={
              selectedAccounts[verifyMethod!]?.contactInfo || "Authenticator"
            }
            theme={theme}
            values={values}
            handleBlur={handleBlur}
            setFieldValue={setFieldValue}
            onBack={handleBack}
            onResend={handleResendCode}
            countDown={countDown}
          />
        ) : (
          <Box className="overflow-hidden">
            <Typography
              variant="body1"
              color="text.primary"
              className="text-gray-700 font-bold mb-1"
            >
              Select authentication method
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              className="text-[#797E8B] mb-5"
            >
              Verification code verification is required.
            </Typography>
            <Stack spacing={theme.spacing(2)} sx={{ mt: 1 }}>
              {authMethodConfigs.map((item) => (
                <AuthMethodCard key={item.dataMethod} {...item} />
              ))}
            </Stack>
            <Typography
              variant="body2"
              color="text.secondary"
              className="text-[#515666] mt-5 text-left"
            >
              Having trouble receiving the verification code? Please contact us
              at
              <Link
                href="mailto:<EMAIL>"
                underline="hover"
                color="primary"
                sx={{ ml: 0.5 }}
              >
                <EMAIL>
              </Link>
            </Typography>
          </Box>
        )}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleCloseMenu}
          MenuListProps={{
            "aria-labelledby": "basic-button",
          }}
        >
          {account?.map((contact) => (
            <MenuItem
              key={contact.id}
              onClick={() => handleSelectAccount(contact)}
            >
              {contact.contactInfo}
            </MenuItem>
          ))}
        </Menu>
      </DialogContent>
    </Dialog>
  );
}
