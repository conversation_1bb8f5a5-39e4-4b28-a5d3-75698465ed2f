interface RequestOptions extends RequestInit {
  data?: any;
  params?: Record<string, string | number | boolean | null | undefined>;
}

export async function request<T = any>(url: string, options: RequestOptions = {}): Promise<T> {
  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/x-www-form-urlencoded',
    'Accept': 'application/json',
  };

  const headers = new Headers({
    ...defaultHeaders,
    ...(options.headers || {}),
  });

  // 处理FLP请求参数签名校验
  const headerManager = createRequestHeaderManager();
  if (window?.LFP) {
    window?.LFP?.init(headerManager, {
      url,
      data: options.params || options.data || {},
      contentType: headers.get('Content-Type') || '',
      type: options.method || 'GET',
    })
  }

  const customHeaders = headerManager.getAllRequestHeaders();
  for (const key in customHeaders) {
    if (Object.prototype.hasOwnProperty.call(customHeaders, key)) {
      const value = customHeaders[key];
      if (value !== null && value !== undefined) {
        headers.set(key, String(value));
      }
    }
  }

  let body: BodyInit | undefined;
  if (options.data !== undefined) {
    let processedData = options.data;
    if (
      processedData &&
      typeof processedData === 'object' &&
      !Array.isArray(processedData) &&
      !(processedData instanceof FormData) &&
      !(processedData instanceof URLSearchParams) &&
      !(processedData instanceof Blob) &&
      !(processedData instanceof ArrayBuffer) &&
      !(typeof ReadableStream !== 'undefined' && processedData instanceof ReadableStream)
    ) {
      const cleanedData: Record<string, any> = {};
      let hasProperties = false;
      for (const key in processedData as Record<string, any>) {
        if (Object.prototype.hasOwnProperty.call(processedData, key)) {
          const value = (processedData as Record<string, any>)[key];
          if (value !== null && value !== undefined) {
            cleanedData[key] = value;
            hasProperties = true;
          }
        }
      }
      if (hasProperties) {
        processedData = cleanedData;
      } else if (Object.keys(options.data).length > 0) {
        processedData = undefined;
      } else {
        processedData = {};
      }
    }

    if (processedData !== undefined) {
      const method = options.method || 'GET';
      const contentType = headers.get('Content-Type');

      // 检查是否是需要发送请求体的方法
      const methodsWithBody = ['POST', 'PUT', 'PATCH'];
      if (methodsWithBody.includes(method.toUpperCase())) {
        if (contentType && contentType.includes('application/x-www-form-urlencoded') &&
          processedData &&
          typeof processedData === 'object' &&
          !Array.isArray(processedData) &&
          !(processedData instanceof FormData) &&
          !(processedData instanceof URLSearchParams) &&
          !(processedData instanceof Blob) &&
          !(processedData instanceof ArrayBuffer) &&
          !(typeof ReadableStream !== 'undefined' && processedData instanceof ReadableStream)
        ) {
          // 转换为 URLSearchParams
          body = new URLSearchParams(processedData).toString();
        } else if (
          processedData instanceof FormData ||
          processedData instanceof Blob ||
          processedData instanceof ArrayBuffer ||
          (typeof ReadableStream !== 'undefined' && processedData instanceof ReadableStream) ||
          typeof processedData === 'string'
        ) {
          // 直接作为 body
          body = processedData as BodyInit;
        } else {
          // 默认为 JSON
          try {
            body = JSON.stringify(processedData);
          } catch (error) {
            console.error('Error stringify request data:', error);
            return Promise.reject(new Error('Invalid request data'));
          }
        }
      } else {
        // GET/HEAD 请求不应有 body
        body = undefined;
      }
    }
  } else if (options.body) {
    body = options.body;
  }

  let requestUrl = url;
  if (options.params) {
    const queryParams = new URLSearchParams();
    for (const key in options.params) {
      if (Object.prototype.hasOwnProperty.call(options.params, key)) {
        const value = options.params[key];
        if (value !== null && value !== undefined && value !== '') {
          queryParams.append(key, String(value));
        }
      }
    }
    const queryString = queryParams.toString();
    if (queryString) {
      requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
    }
  }

  const fetchOptions: RequestInit = {
    method: options.method || 'GET',
    headers: headers,
    body: body,
    // 'same-origin': Cookie 会随同源 URL 请求发送。这也是 fetch() API 的默认行为。
    // 'include': Cookie 会随所有 URL 请求发送（包括跨域）。
    // 'omit': 不发送 Cookie。
    credentials: options.credentials !== undefined ? options.credentials : 'same-origin',
    mode: options.mode || "cors", // 用户可以指定 'cors', 'no-cors', 'navigate', 'same-origin' (fetch 默认为 'cors')
    cache: options.cache,
    redirect: options.redirect,
    referrerPolicy: options.referrerPolicy,
    integrity: options.integrity,
    keepalive: options.keepalive,
    signal: options.signal,
  };

  if (fetchOptions.method === 'GET' || fetchOptions.method === 'HEAD') {
    delete fetchOptions.body;
    if (headers.get('Content-Type') === 'application/json') {
      headers.delete('Content-Type');
    }
  }

  try {
    const response = await fetch(requestUrl, fetchOptions);

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { status: response.status, statusText: response.statusText, message: response.statusText };
      }
      const error = new Error(errorData.message || `HTTP error! status: ${response.status}`) as any;
      error.response = response;
      error.data = errorData;
      error.status = response.status;
      return Promise.reject(error);
    }

    if (response.status === 204 || response.headers.get('Content-Length') === '0') {
      return Promise.resolve(null as unknown as T);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json() as Promise<T>;
    } else {
      return response.text().then(text => {
        try {
          return JSON.parse(text) as T;
        } catch (e) {
          return text as unknown as T;
        }
      });
    }

  } catch (error: any) {
    console.error('Fetch request failed:', error);
    if (error.name === 'AbortError') {
      return Promise.reject(new Error('Request aborted'));
    }
    return Promise.reject(error);
  }
}

interface RequestHeaderManager {
  setRequestHeader: (name: string, value: string) => void;
  getRequestHeader: (name: string) => string | undefined;
  getAllRequestHeaders: () => Record<string, string>;
}

export function createRequestHeaderManager(): RequestHeaderManager {
  const headers: Record<string, string> = {};

  const setRequestHeader = (name: string, value: string): void => {
    headers[name.toLowerCase()] = String(value); // Normalize header names to lowercase
  };

  const getRequestHeader = (name: string): string | undefined => {
    if (typeof name !== 'string') {
      console.warn("Header name must be a string.");
      return undefined;
    }
    return headers[name.toLowerCase()]; // Normalize for lookup
  };

  const getAllRequestHeaders = (): Record<string, string> => {
    return { ...headers }; // Return a copy to prevent direct modification
  };

  return {
    setRequestHeader,
    getRequestHeader,
    getAllRequestHeaders,
  };
}

// 以下是为兼容FLP请求参数而添加的，不要删除
if (typeof window !== 'undefined') {
  window.$ = {
    ajax(options: { url: string } & RequestOptions) {
      const { url, ...otherOptions } = options;
      if (otherOptions.data) {
        if (otherOptions.method && otherOptions.method.toUpperCase() === 'GET') {
          otherOptions.params = otherOptions.data;
          delete otherOptions.data;
        }
      }
      return request(url, options);
    },
  }
}