"use client";

import { useFormik } from "formik";
import { useSnackbar } from "notistack";
import Button from "@/ui/atoms/Button";
import { CodeInput } from "@/ui/atoms/CodeInput";
import * as yup from "yup";

const validationSchema = yup.object({
  code: yup.string().required("Verification code is required"),
});

interface VerifyCodeProps {
  loading?: boolean;
  onVerify: (code: string) => void; // Changed from setVerificationCode to onVerify
  resendCode?: () => void;
  type?: "login" | "signup";
}

export default function ConfirmSignUpForm({
  loading,
  resendCode,
  onVerify, // Changed from setVerificationCode to onVerify
}: VerifyCodeProps) {
  const { enqueueSnackbar } = useSnackbar();

  const {
    values,
    errors,
    handleBlur,
    setFieldValue,
    handleSubmit,
    setFieldTouched,
    validateForm,
    touched,
  } = useFormik(
    // Added touched
    {
      validationSchema,
      initialValues: {
        code: "",
      },
      validateOnBlur: true,
      validateOnChange: false,
      onSubmit: onSubmit,
    }
  );

  async function onSubmit(values: { code: string }) {
    // 先触发校验
    const formErrors = await validateForm();
    setFieldTouched("code", true, true);
    if (formErrors.code) {
      enqueueSnackbar(formErrors.code, {
        variant: "error",
      });
      return;
    }
    if (!values.code || values.code.length !== 6) {
      enqueueSnackbar("Please enter the 6-digit verification code.");
      return;
    }
    onVerify(values.code); // Call onVerify with the code
  }

  // when user input 6 digit code, submit form automatically
  // useEffect(() => {
  //   if (isReadyToConfirmCode && values.code.length === 6) {
  //     handleSubmit();
  //   }
  // }, [handleSubmit, isReadyToConfirmCode, values.code.length]);

  return (
    <form
      className="flex flex-col justify-center h-full max-w-[420px] m-auto"
      onSubmit={handleSubmit}
      data-testid="confirm-signup-form-root"
    >
      <h1
        className={`mb-8 text-2xl font-bold`}
        data-testid="confirm-signup-form-title"
      >
        Check your email for code
      </h1>
      <h4
        className="mb-4 text-sm text-[#515666]"
        data-testid="confirm-signup-form-label"
      >
        6 Digit Verification Code
      </h4>
      <CodeInput
        name="code"
        id="code"
        value={values.code}
        disabled={loading}
        onBlur={handleBlur}
        setFieldValue={setFieldValue}
        error={touched.code && errors.code ? errors.code : undefined} // Display error if touched and error exists
        dataTestIdPrefix="confirm-signup-form-code"
      />
      <div
        className="mt-8 mb-8 text-sm text-center"
        data-testid="confirm-signup-form-resend"
      >
        Don&#39;t see a code?{" "}
        <span
          className="text-primary-main cursor-pointer"
          onClick={resendCode}
          data-testid="confirm-signup-form-resend-btn"
        >
          Resend to email
        </span>
      </div>
      <Button
        className="w-full h-[40px] font-normal"
        variant="contained"
        type="submit"
        disabled={loading}
        loading={loading}
        data-testid="confirm-signup-form-submit"
      >
        Verify
      </Button>
    </form>
  );
}
