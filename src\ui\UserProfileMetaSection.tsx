import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Checkbox,
  FormControl,
  FormLabel,
  FormHelperText,
} from "@mui/material";
import { useFormik } from "formik";
import { useEffect } from "react";
import * as yup from "yup";

import Input from "@/ui/atoms/Input";
import Select from "@/ui/atoms/Select";

export interface IUserProfileMetaValues {
  companyTradeName: string;
  software: string;
  interested: string[];
}

interface IUserProfileMetaSectionProps {
  title?: string;
  buttonText?: string;
  isLoading?: boolean;
  profileMetaValues: IUserProfileMetaValues;
  callback?: () => void;
  onUpdate?: (values: IUserProfileMetaValues) => void;
}

const interestedOptions = [
  "Healthcheck",
  "Sales CRM",
  "Lettings",
  "Accounting Service",
];

export default function UserProfileMetaSection({
  title = "Complete Registration",
  buttonText = "Next",
  isLoading = false,
  profileMetaValues = {
    companyTradeName: "",
    software: "",
    interested: [],
  },
  callback,
  onUpdate,
}: IUserProfileMetaSectionProps) {
  const validationSchema = yup.object({
    companyTradeName: yup.string().required("Company Trading Name is required"),
    software: yup.string().required("Cannot be empty"),
    interested: yup
      .array()
      .of(yup.string().required("Product name is required"))
      .min(1, "At least one product must be selected")
      .required("At least one product must be selected"),
  });

  const { values, handleBlur, handleChange, errors, touched, handleSubmit } =
    useFormik({
      initialValues: {
        ...profileMetaValues,
      },
      validationSchema,
      onSubmit: handleSubmitForm,
      validateOnChange: true,
      validateOnBlur: true,
      enableReinitialize: true,
    });

  useEffect(() => {
    if (onUpdate) {
      onUpdate(values);
    }
  }, [values, onUpdate]);

  function handleSubmitForm() {
    callback?.();
  }

  return (
    <div className="w-full" data-testid="user-profile-meta-section-root">
      <Typography
        variant="h2"
        component="h2"
        fontSize={24}
        fontWeight={700}
        data-testid="user-profile-meta-section-title"
      >
        {title}
      </Typography>

      <form
        onSubmit={handleSubmit}
        className="flex flex-col mt-[30px]"
        data-testid="user-profile-meta-section-form"
      >
        <FormControl
          className="gap-1.5 text-[#a0a3af] mb-2.5"
          required
          data-testid="user-profile-meta-section-interested"
        >
          <FormLabel id="interested-softwares">
            Please select the product you are interested in
          </FormLabel>
          <div className="grid grid-cols-2 gap-2.5">
            {interestedOptions.map((item) => (
              <Checkbox
                key={item}
                name="interested"
                value={item}
                checked={values.interested.includes(item)}
                onChange={handleChange}
                className="p-0"
                icon={
                  <Button
                    variant="outlined"
                    className="w-full h-10 text uppercase"
                    data-testid={`user-profile-meta-section-interested-btn-${item}`}
                  >
                    {item}
                  </Button>
                }
                checkedIcon={
                  <Button
                    variant="contained"
                    className="w-full h-10 text uppercase"
                    data-testid={`user-profile-meta-section-interested-btn-checked-${item}`}
                  >
                    {item}
                  </Button>
                }
                inputProps={
                  {
                    "data-testid": `user-profile-meta-section-interested-checkbox-${item}`,
                  } as any
                }
              />
            ))}
          </div>
          <div className="w-full">
            {errors.interested && touched.interested ? (
              <FormHelperText error className="mx-0">
                {errors.interested}
              </FormHelperText>
            ) : null}
          </div>
        </FormControl>
        <Input
          label="Company Trading Name"
          required
          name="companyTradeName"
          id="companyTradeName"
          size="small"
          value={values.companyTradeName}
          onBlur={handleBlur}
          onChange={handleChange}
          placeholder="Enter Name"
          error={!!errors.companyTradeName && touched.companyTradeName}
          helperText={
            errors.companyTradeName && touched.companyTradeName
              ? errors.companyTradeName
              : " "
          }
          inputProps={{
            "data-testid": "user-profile-meta-section-company-trade-name",
          }}
        />

        <Select
          label="What software do you use for your client accounting?"
          required
          placeholder="Select"
          name="software"
          id="software"
          value={values.software}
          onBlur={handleBlur}
          onChange={handleChange}
          error={!!errors.software && touched.software}
          helperText={
            errors.software && touched.software ? errors.software : " "
          }
          inputProps={{ "data-testid": "user-profile-meta-section-software" }}
        />

        <Button
          variant="contained"
          type="submit"
          className="w-full mt-5 h-10"
          disabled={isLoading}
          data-testid="user-profile-meta-section-submit"
        >
          {buttonText}
        </Button>
      </form>
    </div>
  );
}
