declare global {
  interface Window {
    LoftyAuthServe: any;
    $: {
      ajax: (options: any) => void
    }
    LFP: {
      init: (headerManager: any, options: any) => void
    }
  }
}

declare module '*.svg' {
  import { FC, SVGProps } from 'react'
  const content: FC<SVGProps<SVGElement>>
  export default content
}

declare module "*.svg?url" {
  const content: string;
  export default content;
}

export {}
