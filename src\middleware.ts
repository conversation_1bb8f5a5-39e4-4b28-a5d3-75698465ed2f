import { type NextRequest, NextResponse } from "next/server";
import { getConfig } from './utils/server-config';

export async function middleware(request: NextRequest) {
  // Critical fix: Check and allow Server Action requests
  if (request.headers.get('next-action')) {
    return NextResponse.next();
  }

  const { pathname } = new URL(request.url);
  const hostname = request.headers.get("x-forwarded-host") || request.headers.get("host");
  const config = getConfig(hostname!);

  // Unified API proxy rewrite logic
  if (pathname.startsWith('/api/authcenter/')) {
    const destination = `${config.LOFTY_AUTH_SERVE_BASE_URL}${pathname}`;
    return NextResponse.rewrite(new URL(destination, request.url));
  }
  if (pathname.startsWith('/api/user-account/')) {
    const destination = `${config.LOFTY_SALES_SERVE_BASE_URL}${pathname}`;
    return NextResponse.rewrite(new URL(destination, request.url));
  }
  if (pathname.startsWith('/api/gateway/')) {
    const destination = `${config.LOFTY_SALES_SERVE_BASE_URL}${pathname}`;
    return NextResponse.rewrite(new URL(destination, request.url));
  }

  // Retain other existing logic
  const response = NextResponse.next({
    headers: {
      'next-url': pathname,
      'next-search': new URL(request.url).search,
    }
  });

  if (['/reset-password', '/login', '/signup'].includes(pathname)) {
    return response;
  }

  return response;
}

export const config = {
  /*
   * Match all request paths except for the ones starting with
   */
  matcher: [
    "/((?!_next/static|privacy|terms|_next/image|.*\\.png$|.*\\.svg|.*\\.otf).*)",
  ],
};
