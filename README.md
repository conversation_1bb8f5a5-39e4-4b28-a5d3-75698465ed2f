# Loftyworks official

## 项目概述

本项目是 Loftyworks 官方认证中心的配套前端应用，负责用户认证、授权以及相关的用户界面交互。

## 技术栈

本项目使用以下技术：

*   **框架**: [Next.js](https://nextjs.org/) (v14.1.4) - 一个用于构建 Web 应用的 React 框架。
*   **语言**: [TypeScript](https://www.typescriptlang.org/) (v5.8.3) - JavaScript 的一个类型化超集。
*   **UI 库**:
    *   [Material-UI (MUI)](https://mui.com/) (v6.1.10) - 一个流行的 React UI 框架。
    *   [Tailwind CSS](https://tailwindcss.com/) (v3.4.17) - 一个实用至上的 CSS 框架。
    *   [Heroicons](https://heroicons.com/) (v2.2.0) - 一套精美的手工制作 SVG 图标。
*   **表单处理**:
    *   [Formik](https://formik.org/) (v2.4.6) - 一个用于 React 的表单库。
    *   [Yup](https://github.com/jquense/yup) (v1.6.1) - 一个用于运行时值解析和验证的模式构建器。
*   **通知**: [Notistack](https://notistack.com/) (v3.0.1) - 一个用于显示通知的库。
*   **安全**: [react-google-recaptcha-v3](https://github.com/t49tran/react-google-recaptcha-v3) (v1.10.1) - 一个用于集成 Google reCAPTCHA v3 的库。
*   **加密**: [crypto-js](https://github.com/brix/crypto-js) (v4.2.0) - 一个加密标准库。
*   **工具库**:
    *   [clsx](https://github.com/lukeed/clsx) (v2.1.1) - 一个用于有条件地构造 `className` 字符串的小工具。
    *   [tailwind-merge](https://github.com/dcastil/tailwind-merge) (v2.5.5) - 一个用于合并 Tailwind CSS 类而不产生样式冲突的工具。
*   **包管理器**: [PNPM](https://pnpm.io/) (v9.12.3) - 一个快速、节省磁盘空间的包管理器。
*   **代码检查**: [ESLint](https://eslint.org/) (v8.57.1) - 一个用于 JavaScript 和 TypeScript 的可插拔代码检查工具。
*   **CSS 处理**: [PostCSS](https://postcss.org/) (v8.5.3) - 一个用于通过 JavaScript 插件转换 CSS 的工具。
*   **构建工具**: [Next.js](https://nextjs.org/) (v14.1.4) - Next.js 包含自己的构建系统。

## 快速开始

首先，运行开发服务器：

```bash
pnpm install
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 在浏览器中查看结果。

## 部署

部署到 AWS 开发环境：

1. 更新 AWS 凭据，使用 `chime-rentancy` 账户的默认配置文件。
2. 运行 shell 命令：
   `source ./deploys/aws-deploy.sh`

## 了解更多

要了解更多关于 Next.js 的信息，请参考以下资源：

- [Next.js 文档](https://nextjs.org/docs) - 了解 Next.js 的特性和 API。
- [学习 Next.js](https://nextjs.org/learn) - 一个交互式的 Next.js 教程。

## Docker build 

1. [Install Docker](https://docs.docker.com/get-docker/) on your machine.
1. Build your container: `docker build -t loftywroks-official-fe:latest .`.
1. Run your container: `docker run -p 3000:3000 loftywroks-official-fe:latest`.

You can view your images created with `docker images`.