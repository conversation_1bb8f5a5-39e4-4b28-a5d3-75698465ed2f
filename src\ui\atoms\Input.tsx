import {
  FormLabel,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { ComponentProps, useState } from "react";
import { twMerge } from "tailwind-merge";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/solid";

type InputWithLabelProps = {
  label?: string;
  classes?: {
    label?: string;
  };
} & ComponentProps<typeof TextField>;

export default function Input({
  label,
  required,
  classes,
  className,
  inputProps,
  InputProps,
  maxLength,
  ...props
}: InputWithLabelProps & { maxLength?: number }) {
  const [showPassword, setShowPassword] = useState(false);
  const isPasswordType = props.type === "password";
  return (
    <FormLabel
      className={twMerge("flex flex-col text-sm gap-1.5", classes?.label)}
    >
      <span className="">
        {label}
        {required && <span className="text-error"> *</span>}
      </span>
      <TextField
        className={twMerge("", className)}
        InputProps={{
          ...InputProps,
          ...(isPasswordType
            ? {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label={
                        showPassword ? "Hide password" : "Show password"
                      }
                      onClick={() => setShowPassword((v) => !v)}
                      // onMouseEnter={() => setShowPassword(true)}
                      // onMouseLeave={() => setShowPassword(false)}
                      edge="end"
                      tabIndex={-1}
                    >
                      {showPassword ? (
                        <EyeSlashIcon width={18} height={18} color="#C6C8D1" />
                      ) : (
                        <EyeIcon width={18} height={18} color="#C6C8D1" />
                      )}
                    </IconButton>
                  </InputAdornment>
                ),
              }
            : {}),
        }}
        inputProps={{
          ...inputProps,
          ...(maxLength ? { maxLength } : {}),
        }}
        {...props}
        type={
          isPasswordType ? (showPassword ? "text" : "password") : props.type
        }
      />
    </FormLabel>
  );
}
