import type { Config } from "tailwindcss";

export default {
  content: ["./src/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    extend: {
      colors: {
        // 基础色
        "primary-main": "#4845F0",
        "primary-light": "#EFEEFD",
        "secondary-main": "#1BCA96",
        "error-main": "#FF285F",

        // 文本色
        "text-primary": "#202437",
        "text-secondary": "#515666",
        "text-secondary2": "#515666",
        "text-secondary3": "#797e8b",

        // 背景和边框
        "bg-theme": "#e9e9ff",
        "bg-page": "#f8f9fc",
        "border-theme": "#EBECF1",
        "bg-main": "#f4f6f8",

        // 标准色
        purple: "#7145DF",
        orange: "#FFAB00",
        red: "#E9137F",
        green: "#31CFA0",
        "blue-light": "#2492FC",
        "blue-lighter": "#4478FC",
        cyan: "#5D51E2",

        // 状态色
        "status-conveyancing": "#FFD600",
        "status-exchange": "#797E8B",
        "status-offer-accepted": "#1bca96",
        "status-error": "#FF285F",
        "status-active": "#1bca96",
        "status-warning": "#FFD600",

        // 灰色调
        "gray-50": "#FAFBFD",
        "gray-100": "#F9F9FA",
        "gray-200": "#EEEFF1",
        "gray-300": "#E1E2E6",
        "gray-400": "#C6C8D1",
        "gray-500": "#A0A3AF",
        "gray-600": "#797E8B",
        "gray-700": "#515666",
        "gray-800": "#202437",
      },
      height: {
        "header-full": "calc(100vh - 64px)",
        "table-full": "calc(100% - 50px)",
        "table-full-1": "calc(100% - 60px)",
        "table-full-min": "calc(100% - 40px)"
      }
    },
  },

  plugins: [],
  important: "html",
} satisfies Config;
