# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `pnpm install` - Install dependencies
- `pnpm dev` - Start development server (Next.js)
- `pnpm build` - Build the application 
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint

## Architecture Overview

This is a Next.js 14 frontend application for Loftyworks authentication center using the App Router pattern.

### Key Technologies
- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Material-UI (MUI)** + **Tailwind CSS** for styling
- **Formik** + **Yup** for form handling and validation
- **Notistack** for notifications
- **crypto-js** for client-side encryption

### Project Structure

```
src/
├── app/                    # Next.js App Router pages and layouts
│   ├── (auth)/            # Authentication pages (login, signup, reset)
│   ├── globals.css        # Global styles
│   └── layout.tsx         # Root layout
├── ui/                    # Reusable UI components
│   ├── atoms/             # Basic UI components (Button, Input, etc.)
│   ├── auth/              # Authentication-specific components
│   └── svg/               # SVG icons
├── lib/                   # Utility functions and API layer
│   ├── api/               # API endpoints and request handling
│   └── constants.ts       # Application constants
├── core/                  # Core configurations
│   └── theme.ts           # MUI theme configuration
├── assets/                # Static assets (icons, images)
└── @types/                # TypeScript type definitions
```

### Authentication Flow
The application handles authentication through API endpoints defined in `src/lib/api/endpoints.ts`:
- Login/logout functionality
- Two-factor authentication (2FA) 
- Password reset via email
- User registration with verification codes

### Styling Architecture
- **Tailwind CSS** for utility-first styling
- **Material-UI components** with custom theme overrides in `src/core/theme.ts`
- Custom color palette integrated between Tailwind and MUI
- Component-specific styles in respective directories

### Development Patterns
- Components use PascalCase naming matching file names
- UI components are organized in atomic design pattern
- API calls are centralized in `src/lib/api/`
- Type definitions are centralized in `src/@types/`
- Forms use Formik with Yup validation schemas

## Deployment

AWS deployment via: `source ./deploys/aws-deploy.sh`

## Docker Support

```bash
docker build -t loftywroks-official-fe:latest .
docker run -p 3000:3000 loftywroks-official-fe:latest
```