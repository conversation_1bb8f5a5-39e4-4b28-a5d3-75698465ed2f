.dropdown {
  position: relative;
}

.dropdown.open .dropdownContent {
  display: block;
}

.dropdown.open .dropdownIcon {
  transform: rotateZ(0);
}

.dropdownLabel {
  font-size: 14px;
  line-height: 20px;
  color: #515666;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.dropdownContent {
  display: none;
  position: absolute;
  top: 24px;
  right: 0;
  z-index: 999;
  max-height: 332px;
  overflow-y: auto;
  width: 240px;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #E1E2E6;
  box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.1);
  padding: 6px 0;
  transition: all 0.3s;
}

.dropdownItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 32px;
  padding: 6px 15px;
  line-height: 20px;
  font-size: 14px;
  color: #515666;
  cursor: pointer;
}

.dropdownIcon {
  transform-origin: center;
  transform: rotateZ(180deg);
  margin-left: 5px;
  transition: 0.3s;
}

.dropdownItem:hover {
  background-color: #F6F7FB;
}

.dropdownItem.active {
  color: #00ACDB;
}