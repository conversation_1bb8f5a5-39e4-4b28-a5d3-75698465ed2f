"use client";

import { CheckCircleIcon } from "@heroicons/react/24/solid";
import { Box, Button, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import { useCallback } from "react";

export default function ResetPasswordResult() {
  const router = useRouter();

  const handleRedirect = useCallback(() => {
    router.push("/");
  }, [router]);

  return (
    <Box className="flex flex-col justify-center items-center w-[420px] h-full" data-testid="reset-password-result-root">
      <Box className="mb-8 w-[70px] h-[70px] shadow-md rounded-full flex justify-center items-center" data-testid="reset-password-result-icon">
        <CheckCircleIcon className="w-16 h-16" color="#15D776" />
      </Box>
      <Typography
        variant="h4"
        className="font-bold text-center mb-2.5 text-[#202437] text-2xl"
        data-testid="reset-password-result-title"
      >
        Password reset!
      </Typography>
      <Typography
        variant="body1"
        className="text-center text-[#A0A3AF] mb-8 text-sm"
        data-testid="reset-password-result-desc"
      >
        Your password has been successfully reset.
        <br />
        Click below to login
      </Typography>
      <Button
        variant="contained"
        className="w-full"
        size="large"
        onClick={handleRedirect}
        data-testid="reset-password-result-login"
      >
        Return to login
      </Button>
    </Box>
  );
}
