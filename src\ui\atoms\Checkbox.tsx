import { Checkbox as CheckboxMUI } from "@mui/material";
import { ComponentProps } from "react";
import CheckboxUnchecked from "../svg/CheckboxUnchecked.svg?url";
import CheckboxChecked from "../svg/CheckboxChecked.svg?url";
import Image from "next/image";

type CheckboxProps = {} & ComponentProps<typeof CheckboxMUI>;

export default function Checkbox({ inputProps, ...props }: CheckboxProps) {
  return (
    <CheckboxMUI
      {...props}
      inputProps={inputProps}
      icon={
        <Image src={CheckboxUnchecked} alt="checkIcon" width={16} height={16} />
      }
      checkedIcon={
        <Image src={CheckboxChecked} alt="checkIcon" width={16} height={16} />
      }
    />
  );
}
