import type { Metadata } from "next";
import "./globals.css";
import { ThemeProvider } from "@mui/material";
import { theme } from "@/core/theme";
import Script from "next/script";
import { NotistackProvider } from "@/lib/SnackbarProviderClient";
import StyleSnackbar from "@/ui/atoms/StyleSnackbar";
import Image from "next/image";

export const metadata: Metadata = {
  title: "LoftyWorks Official",
  description: "LoftyWorks Official Portal",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html className="bg-background-light" lang="en">
      <head>
        <Script async src="https://web-sdk.smartlook.com/recorder.js"></Script>
        <Script id="smartlook">
          {`window.addEventListener('load', () => {
          if (typeof smartlook === 'function') {
            smartlook('init', 'e6196e28119521832c353f9186b31ac232071fc8', { region: 'eu' });
          }
        });`}
        </Script>
        <Script async id="mixpanelInsert">
          {`
          (function(f, b) {
            if (!b.__SV) {
                var e, g, i, h;
                window.mixpanel = b;
                b._i = [];
                b.init = function(e, f, c) {
                    function g(a, d) {
                        var b = d.split(".");
                        2 == b.length && ((a = a[b[0]]), (d = b[1]));
                        a[d] = function() {
                            a.push([d].concat(Array.prototype.slice.call(arguments, 0)));
                        };
                    }
                    var a = b;
                    "undefined" !== typeof c ? (a = b[c] = []) : (c = "mixpanel");
                    a.people = a.people || [];
                    a.toString = function(a) {
                        var d = "mixpanel";
                        "mixpanel" !== c && (d += "." + c);
                        a || (d += " (stub)");
                        return d;
                    };
                    a.people.toString = function() {
                        return a.toString(1) + ".people (stub)";
                    };
                    i = "disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");
                    for (h = 0; h < i.length; h++) g(a, i[h]);
                    var j = "set set_once union unset remove delete".split(" ");
                    a.get_group = function() {
                        function b(c) {
                            d[c] = function() {
                                call2_args = arguments;
                                call2 = [c].concat(Array.prototype.slice.call(call2_args, 0));
                                a.push([e, call2]);
                            };
                        }
                        for (var d = {}, e = ["get_group"].concat(Array.prototype.slice.call(arguments, 0)), c = 0; c < j.length; c++) b(j[c]);
                        return d;
                    };
                    b._i.push([e, f, c]);
                };
                b.__SV = 1.2;
                e = f.createElement("script");
                e.type = "text/javascript";
                e.async = !0;
                e.src = "https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";
                g = f.getElementsByTagName("script")[0];
                g.parentNode.insertBefore(e, g);
            }
          })(document, window.mixpanel || []);
        `}
        </Script>
        <Script
          async
          id="LFP"
          src="https://static.chimeroi.com/servicetool-temp/20231120/23/374bc506-600a-437b-a7b6-e3967b5510ac_fingerprint-prod-21112023.js"
        />
      </head>
      <body className={`antialiased`}>
        <ThemeProvider theme={theme}>
          <NotistackProvider
            maxSnack={5}
            anchorOrigin={{ vertical: "top", horizontal: "center" }}
            hideIconVariant={true}
            autoHideDuration={2000}
            Components={{
              default: StyleSnackbar,
              success: StyleSnackbar,
              error: StyleSnackbar,
              info: StyleSnackbar,
            }}
          >
            <div className="fixed bottom-5 left-5 z-[1000] pointer-events-none">
              <Image
                src="/watermark.svg"
                alt="watermark"
                width={56}
                height={20}
              />
            </div>
            {children}
          </NotistackProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
