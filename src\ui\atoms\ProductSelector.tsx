import {
  FormLabel,
  TextField,
  MenuItem,
  Checkbox,
} from '@mui/material';
import { twMerge } from 'tailwind-merge';
export default function Select({
  label,
  required,
  classes,
  className,
  value,
  ...props
}: any) {
  const items = [
    {
      value: 'Healthcheck',
      label: 'Healthcheck',
    },
    {
      value: 'Lofty',
      label: 'Lofty',
    },
    {
      value: 'LoftyWorks',
      label: 'LoftyWorks',
    },
    {
      value: 'LoftyPay',
      label: 'LoftyPay',
    },
  ];

  return (
    <FormLabel
      className={twMerge('flex flex-col text-sm gap-1.5', classes?.label)}
    >
      <span className="">
        {label}
        {required && <span className="text-error"> *</span>}
      </span>
      <TextField
        className={twMerge('', className)}
        value={value}
        {...props}
        select
        slotProps={{
          select: {
            multiple: true,
            displayEmpty: true,
            classes: { multiple: 'p-2.5 text-sm' },
            renderValue: (selected: Array<string>) => {
              if (selected.length === 0) {
                return <span className="text-gray-400">Select</span>;
              } else {
                return selected.join(', ');
              }
            },
          },
        }}
      >
        {items.map((item) => (
          <MenuItem key={item.value} value={item.value}>
            <Checkbox
              size="small"
              classes={{ root: 'py-0 px-1' }}
              checked={value.includes(item.value)}
            />
            <span>{item.label}</span>
          </MenuItem>
        ))}
      </TextField>
    </FormLabel>
  );
}
