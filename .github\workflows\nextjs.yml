name: Deploy Next.js site to Pages

on:
  pull_request:
    branches: 
      - master 
  push:
    branches:
      - master
      - feature/*
      - feat/*
      - fix/*

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment, skipping runs queued between the run in-progress and latest queued.
# However, do NOT cancel in-progress runs as we want to allow these production deployments to complete.
concurrency:
  group: "tlp_build_test"
  cancel-in-progress: false

jobs:
  # Build job
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Setup Node
        uses: actions/setup-node@v4

      - name: Checkout Me
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install dependencies
        run: yarn install

      - name: Build with Next.js
        run: yarn build

      - name: Test with Jest
        run: yarn test
