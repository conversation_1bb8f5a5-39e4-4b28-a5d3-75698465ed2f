import md5 from "crypto-js/md5";
import { request } from "@/lib/utils/request";
import { API_ENDPOINTS } from "./endpoints";

// const BASE_URL = process.env.NEXT_PUBLIC_LOFTY_AUTH_SERVE_BASE_URL;
const CLIENT_ID = process.env.NEXT_PUBLIC_LOFTY_AUTH_SERVE_CLIENT_ID;

export interface LoginRequest {
  email: string;
  password: string;
  token?: string;
  redirectUri?: string;
}

export enum MfaVerifyCodeType {
  EMAIL = 0,
  SMS = 1,
  CALL = 2,
  AUTHENTICATOR = 3,
}

export type MfaVerifyCodeKey = keyof typeof MfaVerifyCodeType;

export const MfaVerifyCodeKeys: Record<MfaVerifyCodeKey, MfaVerifyCodeKey> = {
  EMAIL: "EMAIL",
  SMS: "SMS",
  CALL: "CALL",
  AUTHENTICATOR: "AUTHENTICATOR",
};


export interface MfaSendVerifyCodeRequest {
  sessionId: string;
  accountId: string;
  type: MfaVerify<PERSON>odeKey;
  "g-recaptcha-response": string;
}

export interface MfaVerifyCodeCheckRequest extends MfaSendVerifyCodeRequest {
  redirectUri?: string;
  code: string;
}

export interface LogoutRequest {
  redirect_uri: string;
}

export interface ResetPasswordSendCodeRequest {
  account: string;
}

export interface ResetPasswordVerifyCodeRequest
  extends ResetPasswordSendCodeRequest {
  code: string;
}

export interface ResetPasswordRequest extends ResetPasswordVerifyCodeRequest {
  password: string;
}

export const login = async ({ email, password, token }: LoginRequest) => {
  try {
    const response = await request(API_ENDPOINTS.LOGIN, {
      method: "POST",
      data: {
        account: email,
        email: email,
        password: md5(password).toString(),
        clientId: CLIENT_ID,
        "g-recaptcha-response": token,
      },
    });
    return response;
  } catch (error) {
    console.error("Error logging in:", error);
    throw error;
  }
};

export interface RegisterRequest {
  account: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  phoneCode?: string;
  phoneCountry?: string;
  verificationCode: string;
  source: string;
  busiInterest?: string[];
  companyTradeName?: string;
  softwareUsed?: string;
}
export const register = async (data: RegisterRequest) => {
  return await request(API_ENDPOINTS.REGISTER, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      ...data,
      clientId: CLIENT_ID,
    }
  });
};

export const sendRegisterVerificationCode = async (data: { account: string, type: 'REGISTER' | 'LOGIN' }) => {
  return await request(API_ENDPOINTS.SEND_REGISTER_VERIFICATION_CODE, {
    method: "POST",
    data: {
      ...data,
      codeType: false,
      clientId: CLIENT_ID,
    }
  });
};

export const logout = async (data: LogoutRequest) => {
  try {
    const response = await request(API_ENDPOINTS.LOGOUT, {
      method: "POST",
      data: {
        ...data,
        clientId: CLIENT_ID,
      },
    });
    return response;
  } catch (error) {
    console.error("Error logging out:", error);
    throw error;
  }
};

export const getAuthCode = async () => {
  try {
    const response = await request(API_ENDPOINTS.GET_AUTH_CODE, {
      method: "POST",
      data: {
        clientId: CLIENT_ID,
      },
    });
    return response;
  } catch (error) {
    console.error("Error getting auth code:", error);
    throw error;
  }
};

export const send2faVerificationCode = async (
  data: MfaSendVerifyCodeRequest
) => {
  try {
    const url = API_ENDPOINTS.SEND_2FA_VERIFICATION_CODE;
    const response = await request(url, {
      method: "POST",
      data: {
        ...data,
        clientId: CLIENT_ID,
      },
    });
    return response;
  } catch (error) {
    console.error("Error sending 2FA verification code:", error);
    throw error;
  }
};

export const verify2faCode = async (data: MfaVerifyCodeCheckRequest) => {
  try {
    const url = API_ENDPOINTS.VERIFY_2FA_CODE;
    const response = await request(url, {
      method: "POST",
      data: {
        ...data,
        clientId: CLIENT_ID,
      },
    });
    return response;
  } catch (error) { 
    console.error("Error verifying 2FA code:", error);
    throw error;
  }
};

export const resetPassword = async ({ password, code, account }: ResetPasswordRequest) => {
  try {
    const url = API_ENDPOINTS.RESET_PASSWORD;
    const response = await request(url, {
      headers: {
        "Content-Type": "application/json",
      },
      method: "POST",
      data: {
        password: md5(password).toString(),
        code,
        account,
        clientId: CLIENT_ID,
      },
    });
    return response;
  } catch (error) {
    console.error("Error resetting password:", error);
    throw error;
  }
};

export const sendResetPasswordEmail = async (
  data: ResetPasswordSendCodeRequest
) => {
  try {
    const url = API_ENDPOINTS.SEND_RESET_PASSWORD_EMAIL;
    const response = await request(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        ...data,
        clientId: CLIENT_ID,
      },
    });
    return response;
  } catch (error) {
    console.error("Error sending reset password email:", error);
    throw error;
  }
};

export const verifyResetPasswordToken = async (
  data: ResetPasswordVerifyCodeRequest
) => {
  try {
    const url = API_ENDPOINTS.VERIFY_RESET_PASSWORD_TOKEN;
    const response = await request(url, {
      method: "POST",
      data: {
        ...data,
        clientId: CLIENT_ID,
      },
    });
    return response;
  } catch (error) {
    console.error("Error verifying reset password token:", error);
    throw error;
  }
};

export const createCredentialViaCode = async (data: { code: string }) => {
  try {
    const url = API_ENDPOINTS.CREATE_CREDENTIAL_VIA_CODE;
    const response = await request(url, {
      method: "POST",
      data: {
        ...data,
        clientId: CLIENT_ID,
      },
    });
    return response;
  } catch (error) {
    console.error("Error verifying reset password token:", error);
    throw error;
  }
}

export const checkLogin = async () => {
  try {
    const url = API_ENDPOINTS.CHECK_LOGIN;
    const response = await request(url, {
      method: "POST",
      data: {
        clientId: CLIENT_ID,
      },
    });
    return response;
  } catch (error) {
    console.error("Error checking login status:", error);
    throw error;
  }
}