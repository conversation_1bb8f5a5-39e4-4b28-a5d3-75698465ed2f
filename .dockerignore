# Ignore all files and directories that are not needed in the Docker image or build context

# Node.js specific
node_modules
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*

# Next.js build artifacts
.next/
out/

# Git
.git
.gitignore

# IDE and editor files
.vscode/
.cursor/
.idea/
*.sublime-project
*.sublime-workspace

# macOS specific
.DS_Store

# Logs
*.log

# Test coverage
coverage/

# Deployment scripts
deploys/

# Other temporary or configuration files
.trae/
**/gen-mapping
README.md