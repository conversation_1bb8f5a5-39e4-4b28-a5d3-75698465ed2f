include:
    - project: "devops/gitlab-ci-template"
      file: "/template/js-node-docker-k8s.yaml"
      ref: master

variables:
  APP_NAMESPACE: crm
  APP_NAME: loftyworks-official-frontend

stages:
  - deploy

check:
  stage: deploy
  extends: .check
  when: manual
  needs: []

build:
  stage: deploy
  extends: .build-push-docker-image
  variables:
    APP_NAME: loftyworks-official-frontend
  needs:
    - check

deploy:
  stage: deploy
  variables:
    APP_NAME: loftyworks-official-frontend
    DEPLOY_SERVICE_NAME: loftyworks-official-frontend
    DEPLOY_CONTAINER_NAME: loftyworks-official-frontend
  needs:
    - build
  extends: .eks-deploy

notify:
  stage: deploy
  variables:
    APP_NAME: loftyworks-official-frontend
    DEPLOY_SERVICE_NAME: loftyworks-official-frontend
    DEPLOY_CONTAINER_NAME: loftyworks-official-frontend
  needs:
    - deploy
  extends: .notify

