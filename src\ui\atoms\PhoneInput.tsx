import React, { useState, useEffect } from "react";
import Image from "next/image";
import {
  TextField,
  InputAdornment,
  Menu,
  MenuItem,
  Typography,
  Box,
  ListItemIcon,
  ListItemText,
  Divider,
  TextFieldProps,
} from "@mui/material";
import {
  MagnifyingGlassIcon,
} from "@heroicons/react/24/solid";
import { countryList as rawCountryList } from "../../lib/constants";
import Input from "@/ui/atoms/Input";

// Type guard to ensure a country has all the required properties.
const isCountry = (country: any): country is Country => {
  return (
    typeof country.name === "string" &&
    typeof country.code === "string" &&
    typeof country.dialCode === "string" &&
    typeof country.flag === "string"
  );
};

// Filter the raw list to only include valid Country objects.
const countryList: Country[] = rawCountryList.filter(isCountry);

// Define the type for a country object based on constants.ts
interface Country {
  name: string;
  code: string;
  timeZone: number;
  dialCode: string;
  flag: string;
}

// Define the props for the PhoneInput component
type PhoneInputProps = Omit<TextFieldProps, "value" | "onChange"> & {
  value: string;
  onChange: (phoneNumber: string, country: Country) => void;
  label?: string;
  defaultCountry: string;
};

export const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChange,
  label,
  defaultCountry = 'GB',
  ...rest
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Find US as default or fallback to the first country in the list
  const findDefaultCountry = (code?: string) => {
    return countryList.find((c) => c.code === code) || countryList[0];
  };

  const [selectedCountry, setSelectedCountry] = useState<Country>(() =>
    findDefaultCountry(defaultCountry)
  );

  // Internal state for the numeric part of the phone number
  const [phoneNumber, setPhoneNumber] = useState(value);

  // Filter countries based on the search term
  const filteredCountries = countryList.filter(
    (country) =>
      country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      country.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      country.dialCode.includes(searchTerm)
  );

  // Handle opening the country selection menu
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // Handle closing the country selection menu
  const handleMenuClose = () => {
    setAnchorEl(null);
    setSearchTerm(""); // Reset search on close
  };

  // Handle selecting a new country
  const handleCountrySelect = (country: Country) => {
    setSelectedCountry(country);
    // Call parent's onChange with current number and new country
    onChange(phoneNumber, country);
    handleMenuClose();
  };

  // Handle changes to the phone number input
  const handlePhoneNumberChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    // Allow only digits
    const newNumber = event.target.value.replace(/\D/g, "");
    setPhoneNumber(newNumber);
    // Call parent's onChange with new number and current country
    onChange(newNumber, selectedCountry);
  };

  // Synchronize internal state if the parent's value prop changes
  useEffect(() => {
    setPhoneNumber(value.replace(/\D/g, ""));
  }, [value]);

  // Call onChange with the default country when the component mounts
  useEffect(() => {
    onChange(phoneNumber, selectedCountry);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const open = Boolean(anchorEl);

  return (
    <>
      <Input
        fullWidth
        type="tel" // Use type="tel" for semantic phone number input
        label={label}
        value={phoneNumber}
        onChange={handlePhoneNumberChange}
        placeholder="Phone number"
        {...rest}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    cursor: "pointer",
                    userSelect: "none",
                  }}
                  onClick={handleMenuOpen}
                >
                  <Image
                    src={`https://flagcdn.com/w40/${selectedCountry.code.toLowerCase()}.png`}
                    width={24}
                    height={24}
                    alt={selectedCountry.name}
                    style={{ marginRight: "8px" }}
                    unoptimized
                  />
                  <Typography variant="body1" component="span">
                    {selectedCountry.dialCode}
                  </Typography>
                </Box>
              </Box>
            </InputAdornment>
          ),
        }}
      />
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleMenuClose}
        PaperProps={{
          style: {
            maxHeight: 300,
            width: "320px",
            paddingTop: 0,
          },
        }}
      >
        <Box
          sx={{
            p: 1,
            backgroundColor: "white",
            position: "sticky",
            top: 0,
            zIndex: 1,
            paddingTop: "13px"
          }}
        >
          <TextField
            fullWidth
            variant="outlined"
            size="small"
            placeholder="Search by name, code, or dial code"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onClick={(e) => e.stopPropagation()} // Prevent menu from closing on search click
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <MagnifyingGlassIcon
                    style={{ width: 20, height: 20, color: "gray" }}
                  />
                </InputAdornment>
              ),
            }}
          />
        </Box>
        <Divider />
        <Box sx={{ overflowY: "auto" }}>
          {filteredCountries.map((country) => (
            <MenuItem
              key={country.code}
              onClick={() => handleCountrySelect(country)}
            >
              <ListItemIcon sx={{ minWidth: "auto", mr: 1.5 }}>
                <Image
                  src={`https://flagcdn.com/w40/${country.code.toLowerCase()}.png`}
                  width={24}
                  height={24}
                  alt={country.name}
                  unoptimized
                />
              </ListItemIcon>
              <ListItemText primary={country.name} />
              <Typography variant="body2" color="text.secondary">
                {country.dialCode}
              </Typography>
            </MenuItem>
          ))}
        </Box>
      </Menu>
    </>
  );
};

