"use client";

import { createTheme, PaletteOptions, alpha } from "@mui/material/styles";
import tailwindConfig from "../../tailwind.config";

const colors = tailwindConfig.theme.extend.colors;

const specialColors = {
  deepSkyBlue: {
    100: colors["primary-light"],
    200: colors["gray-200"],
    300: colors["primary-light"],
    400: colors["primary-main"],
    500: colors["cyan"],
  },
};

type CustomColors = {
  specialColors: typeof specialColors;
};

export const loftyworksColors: PaletteOptions & CustomColors = {
  primary: {
    main: colors["primary-main"],
    light: colors["primary-light"],
  },
  secondary: {
    main: colors["secondary-main"],
  },
  background: {
    default: colors["gray-300"],
  },
  text: {
    primary: colors["text-primary"],
    secondary: colors["text-secondary"],
  },
  error: {
    main: colors["error-main"],  
  },
  specialColors,
};

export const theme = createTheme({
  palette: loftyworksColors,
  components: {
    MuiButton: {
      defaultProps: { disableRipple: true },
      styleOverrides: {
        root: { boxShadow: "none", textTransform: "none" },
        contained: {
          color: "white",
          "&:hover": {
            backgroundColor: alpha(colors["primary-main"], 0.8),
            boxShadow: "none"
          },
          "&:active": { backgroundColor: loftyworksColors.specialColors.deepSkyBlue[500], disableRipple: "true", boxShadow: "none" },
          ":disabled": { backgroundColor: loftyworksColors.specialColors.deepSkyBlue[200], color: "white" },
        },
        outlined: {
          border: `1px solid ${loftyworksColors.specialColors.deepSkyBlue[400]}`,
          color: loftyworksColors.specialColors.deepSkyBlue[400],
          "&:hover": { backgroundColor: `rgba(72, 69, 240, 0.1)` },
          "&:active": { backgroundColor: `rgba(72, 69, 240, 0.3)` },
        }
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        asterisk: { color: colors["error-main"] },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          "& .MuiInputBase-input": { fontSize: "14px", height: "40px", boxSizing: "border-box", padding: "10px" },
          "& .MuiInputBase-root.Mui-disabled": { backgroundColor: colors["bg-page"] },
          "& .MuiInputBase-root fieldset": { 
            borderColor: colors["gray-400"], 
            borderWidth: "1px !important"
          },
          "& .MuiInputBase-root:hover fieldset": { borderColor: colors["gray-500"] },
          "& .MuiInputBase-root:focus fieldset": { borderColor: colors["primary-main"] },
          "& .Mui-focused fieldset": { borderColor: `${colors["primary-main"]}` },
          "& .MuiInputBase-root:focus-visible fieldset": { borderColor: colors["primary-main"] },
          "& .MuiInputBase-root:active fieldset": { borderColor: colors["primary-main"] },
          "& .MuiFormHelperText-root": { marginLeft: "0px" },
        },
      },
    },
    MuiMenu: {
      styleOverrides: {
        paper: {
          boxShadow: "none",
          border: `1px solid ${colors["gray-300"]}`,
          borderRadius: "6px",
          padding: "5px"
        },
        list: { padding: 0 },
      },
    },
    MuiMenuItem: {
      defaultProps: { disableRipple: true },
      styleOverrides: {
        root: {
          borderRadius: "3px",
          "&:hover": {
            backgroundColor: loftyworksColors.specialColors.deepSkyBlue[100],
            color: loftyworksColors.specialColors.deepSkyBlue[400]
          },
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          color: colors["primary-main"],
          '&.Mui-checked': {
            color: colors["primary-main"],
          },
        },
      },
    },
  },
});
