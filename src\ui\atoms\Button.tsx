import React from 'react';
import { Button as MaterialButton, CircularProgress, Box } from '@mui/material';
import { ButtonProps as MaterialButtonProps } from '@mui/material/Button'; // 导入MUI Button的Props类型

/**
 * 定义 LoadingButton 组件的Props接口
 * 继承 ButtonProps，并添加自定义的loading相关属性
 * Omit<ButtonProps, 'children' | 'disabled'> 的作用是：
 * 1. 移除原 ButtonProps 中的 'children' 属性，因为我们想更精确地定义它为 React.ReactNode。
 * 2. 移除原 ButtonProps 中的 'disabled' 属性，因为我们的 disabled 状态由 loading 和外部传入的 disabled 共同决定。
 */
interface ButtonProps extends Omit<MaterialButtonProps, 'children' | 'disabled'> {
  /**
   * 是否处于加载状态
   */
  loading?: boolean;
  /**
   * 加载状态时显示的文本，如果未提供则显示原始children
   */
  loadingText?: string;
  /**
   * 加载指示器的大小 (px)
   * @default 20
   */
  spinnerSize?: number;
  /**
   * 加载指示器的颜色，'inherit'会继承按钮的文本颜色
   * @default 'inherit'
   */
  spinnerColor?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | 'inherit';
  /**
   * 按钮的默认内容（非加载状态时显示）
   */
  children: React.ReactNode;
  /**
   * Material-UI的sx prop，用于自定义样式
   */
  sx?: MaterialButtonProps['sx']; // 明确sx的类型
  /**
   * 外部明确禁用按钮
   */
  disabled?: boolean; // 将disabled作为可选属性重新添加，用于与loading状态合并
  'data-testid'?: string; // 新增支持
}

/**
 * 一个带loading状态的MUI按钮组件
 */
const Button: React.FC<ButtonProps> = ({
  loading = false,
  loadingText,
  spinnerSize = 20,
  spinnerColor = 'inherit',
  children,
  sx, // 解构出sx，以便在内部合并
  disabled: externalDisabled = false, // 解构并重命名外部传入的disabled prop
  'data-testid': dataTestId, // 新增支持
  ...otherProps // 捕获所有其他标准的MUI Button组件的props
}) => {
  return (
    <MaterialButton
      {...otherProps} // 透传所有Button的props
      disabled={loading || externalDisabled} // 如果loading或外部明确disabled，则禁用
      sx={{
        // 确保按钮内容在loading时不会被挤压
        // 如果有loadingText，且loading，则minWidth自适应，否则使用默认的MUI min-width
        minWidth: (loading && loadingText) ? 'auto' : undefined,
        position: 'relative', // 用于定位spinner
        display: 'inline-flex', // 确保内部flex布局生效
        alignItems: 'center', // 垂直居中对齐
        justifyContent: 'center', // 水平居中对齐
        ...sx, // 合并外部传入的sx样式
      }}
      data-testid={dataTestId}
    >
      {loading && (
        <CircularProgress
          size={spinnerSize}
          color={spinnerColor}
          sx={{
            // 如果有loadingText，让spinner和文本并排显示
            ...(loadingText ?
                { position: 'static', marginRight: 1 } : // 并排显示，加右边距
                { // 否则，绝对定位居中，覆盖文本
                    position: 'absolute',
                    left: '50%',
                    top: '50%',
                    marginTop: `-${spinnerSize / 2}px`,
                    marginLeft: `-${spinnerSize / 2}px`,
                }
            )
          }}
        />
      )}
      {/*
        当有loadingText时，显示loadingText；
        否则，如果处于loading状态，可以隐藏children（可选），或者显示原始children让spinner覆盖
        这里选择当有loadingText时显示loadingText，没有loadingText时，spinner覆盖在children上
      */}
      {loadingText ? (loading ? loadingText : children) : children}
    </MaterialButton>
  );
};

export default Button;
