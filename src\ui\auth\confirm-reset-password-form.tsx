"use client";

import { useCallback, useState } from "react";
import { useSnackbar } from "notistack";
import Input from "@/ui/atoms/Input";
import Link from "next/link";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import "./style.css";
import * as yup from "yup";
import { sendResetPasswordEmail } from "@/lib/api";
import Button from "@/ui/atoms/Button";

interface IConfirmResetValues {
  email: string;
}

export default function ConfirmResetPasswordForm(props: any) {
  const { setConfirmEmail, setEmail } = props;
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const validationSchema = yup.object({
    email: yup
      .string()
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        "Email is invalid"
      )
      .required("Email is required"),
  });

  const { enqueueSnackbar } = useSnackbar();

  const initialValues: IConfirmResetValues = {
    email: "",
  };

  const { values, handleBlur, errors, touched, handleSubmit, setFieldValue } =
    useFormik({
      onSubmit: onSubmit,
      validationSchema: validationSchema,
      enableReinitialize: true,
      validateOnBlur: true,
      initialValues: initialValues,
    });

  // 包装handleChange，使email输入自动转小写
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<any>) => {
      setFieldValue("email", e.target.value.toLowerCase());
    },
    [setFieldValue]
  );

  async function onSubmit(values: IConfirmResetValues) {
    handleForget(values);
  }

  const handleForget = async (values: IConfirmResetValues) => {
    setIsLoading(true);
    try {
      const resp = await sendResetPasswordEmail({ account: values.email });
      console.log("resp: ", resp);
      if (resp.httpStatus === 200 && resp.ok) {
        enqueueSnackbar("Please check your email.");
        setIsLoading(false);
        setConfirmEmail(1);
        setEmail(values.email);
      } else {
        enqueueSnackbar(resp.status.msg || "Failed to send verify code");
      }
    } catch (error: any) {
      console.error("error: ", error);
      enqueueSnackbar(error?.message || "Failed to send verify code");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form
      className="h-full"
      onSubmit={handleSubmit}
      data-testid="confirm-reset-password-form-root"
    >
      <div
        className={`backItem flex items-center cursor-pointer absolute left-5 top-5`}
        onClick={() => router.back()}
        data-testid="confirm-reset-password-form-back"
      >
        <ArrowLeftIcon className="w-4 h-4 text-[#515666]" />
        <span
          style={{
            fontWeight: 400,
            fontSize: 14,
            lineHeight: "20px",
            marginLeft: "8px",
            color: "#515666",
          }}
        >
          Back
        </span>
      </div>
      <div className="flex flex-col flex-1 justify-center h-full w-[420px] m-auto ">
        <h1
          className={`mb-[30px] text-2xl font-bold`}
          data-testid="confirm-reset-password-form-title"
        >
          Forgot Password
        </h1>
        <div className="w-full">
          <Input
            label="Email"
            // required
            inputProps={{ "data-testid": "confirm-reset-password-form-email" }}
            id="email"
            type="email"
            value={values.email}
            onChange={handleInputChange}
            onBlur={handleBlur}
            disabled={false}
            placeholder="Enter Email"
            error={!!(errors.email && touched.email)}
            helperText={errors.email && touched.email ? errors.email : " "}
          />
        </div>
        <Button
          className="w-full h-[40px] font-normal"
          variant="contained"
          type="submit"
          disabled={isLoading}
          loading={isLoading}
          data-testid="confirm-reset-password-form-submit"
        >
          Send Email
        </Button>

        <div className="mt-8 flex justify-center text-sm items-center">
          {"Don't have an account? "}
          <Link
            href="/signup"
            className="ml-1 cursor-pointer text-sm text-primary-main"
            data-testid="confirm-reset-password-form-signup"
          >
            Sign Up Now!
          </Link>
        </div>
      </div>
    </form>
  );
}
