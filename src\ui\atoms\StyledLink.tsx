import { ReactNode } from "react"

type StyledLinkProps = {
  children: ReactNode;
  href: string;
  className?: string;
  onRedirect?: () => void;
}

export default function StyledLink({ children, href, className, onRedirect }: StyledLinkProps) {
  // use next.js Link can't redirect correctly.
  const jump = () => {
    if (onRedirect) {
      onRedirect?.();
    } else {
      window.location.href = href;
    }
  };
  return (
    <div
      onClick={jump}
      className={`no-underline text-text-secondary ${className ?? ""}`}
    >
      {children}
    </div>
  )
}
