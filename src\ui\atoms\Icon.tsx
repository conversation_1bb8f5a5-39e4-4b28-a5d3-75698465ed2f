import { ReactNode, useEffect, useState } from 'react';

export type IconProps = Record<string, any> & {
  name: string; // public/[name].svg
  className?: string;
};

export default function Icon(props: IconProps) {
  const {
    name,
    className = '',
    ...rest
  } = props;

  const [IconComponent, setIconComponent] = useState<ReactNode | null>(null);
  useEffect(() => {
    import(`@/assets/icon/${name}.svg`).then((module) => {
      setIconComponent(module.default);
    }).catch(err => {});
  }, []);

  if (!IconComponent) {
    return null;
  }
  
  return <span className={className} {...rest}>{IconComponent}</span>;
}