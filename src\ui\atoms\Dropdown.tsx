import { useState } from 'react';
import './dropdown.css';
import Icon from './Icon';

export type Item = {
  label: string;
  value: string;
};

export type DropdownProps = {
  label: string;
  value: string;
  list: Item[];
  dropdownClass?: string;
  dropdownContentClass?: string;
  onChange?: (item: Item) => void;
};

export default function Dropdown({
  label,
  list,
  value,
  dropdownClass = '',
  dropdownContentClass = '',
  ...props
}: DropdownProps) {
  const [open, setOpen] = useState<boolean>(false);
  const handleClick = (item: Item) => {
    setOpen(false);
    props?.onChange?.(item);
  };
  const toggleOpen = () => {
    setOpen(!open);
  };
  return (
    <div
      className={`dropdown ${open ? 'open' : ''} ${dropdownClass}`}
    >
      <div className="dropdownLabel" onClick={toggleOpen}>
        {label}
        <span className="dropdownIcon">
          <Icon name="arrow_up" />
        </span>
      </div>
      <div className={`dropdownContent ${dropdownContentClass}`}>
        {
          list.map((item: Item) =>
          <div
            className={`dropdownItem ${item.value === value ? 'active' : ''}`}
            key={item.value}
            onClick={() => handleClick(item)}
          >
            <span>{item.label}</span>
            {
              item.value === value && <Icon name="check" />
            }
          </div>)
        }
      </div>
    </div>
  );
}
