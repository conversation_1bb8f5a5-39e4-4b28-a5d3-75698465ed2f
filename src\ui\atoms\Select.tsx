import { <PERSON><PERSON>abel, InputLabel, TextField, MenuItem } from "@mui/material";
import { twMerge } from "tailwind-merge";

export default function Select({
  label,
  required,
  classes,
  className,
  placeholder = "Select",
  ...props
}: any) {
  return (
    <FormLabel
      className={twMerge("flex flex-col text-sm gap-1.5", classes?.label)}
    >
      <span className="">
        {label}
        {required && <span className="text-error"> *</span>}
      </span>
      <TextField
        className={twMerge("", className)}
        {...props}
        select
        SelectProps={{
          displayEmpty: true,
          renderValue: (value: string) =>
            value !== "" ? (
              value
            ) : (
              <InputLabel className={`text-sm`}>{placeholder}</InputLabel>
            ),
        }}
      >
        <MenuItem value={"Alto"}>Alto</MenuItem>
        <MenuItem value={"Reapit"}>Reapit</MenuItem>
        <MenuItem value={"Jupix"}>Jupix</MenuItem>
        <MenuItem value={"10ninety"}>10ninety</MenuItem>
        <MenuItem value={"Acquaint"}>Acquaint</MenuItem>
        <MenuItem value={"Veco"}>Veco</MenuItem>
        <MenuItem value={"PayProp"}>PayProp</MenuItem>
        <MenuItem value={"MRI(formerly Qube)"}>MRI(formerly Qube)</MenuItem>
        <MenuItem value={"LettsPay"}>LettsPay</MenuItem>
        <MenuItem value={"Other"}>Other</MenuItem>
        <MenuItem value={"None"}>None</MenuItem>
      </TextField>
    </FormLabel>
  );
}
