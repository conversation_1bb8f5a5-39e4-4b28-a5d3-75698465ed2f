import React from "react";
import { <PERSON>, Typo<PERSON>, <PERSON><PERSON>, <PERSON> } from "@mui/material";
import { CodeInput } from "../../atoms/CodeInput";

interface VerifyCodePanelProps {
  contactInfo: string;
  theme: any;
  values: { code: string };
  handleBlur: any;
  setFieldValue: any;
  onBack: () => void;
  onResend: () => void;
  countDown: number;
}

const VerifyCodePanel: React.FC<VerifyCodePanelProps> = ({
  contactInfo,
  theme,
  values,
  handleBlur,
  setFieldValue,
  onBack,
  onResend,
  countDown,
}) => {
  return (
    <Box className="overflow-hidden">
      <Typography
        variant="body1"
        color="text.primary"
        className="text-[#515666] font-bold text-base leading-5 mb-2.5"
      >
        Enter Verification Code
      </Typography>
      <Typography variant="body2" color="text.secondary" className="mb-5">
        A 6-digit code was sent to{" "}
        <span style={{ color: theme.palette.primary.main }}>{contactInfo}</span>
        . The code expires in 10 minutes.
      </Typography>
      <CodeInput
        name="code"
        id="code"
        value={values.code}
        onBlur={handleBlur}
        setFieldValue={setFieldValue}
        disabled={false}
      />
      <Box display="flex" justifyContent="space-between" sx={{ mt: 3, gap: 2 }}>
        <Button variant="outlined" size="small" onClick={onBack}>
          Back
        </Button>
        <Button onClick={onResend} disabled={countDown > 0}>
          Resend Code {countDown > 0 ? `(${countDown}s)` : ""}
        </Button>
      </Box>
    </Box>
  );
};

export default VerifyCodePanel;
