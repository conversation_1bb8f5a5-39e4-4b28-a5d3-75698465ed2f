"use client";

import { useFormik } from "formik";
import Input from "@/ui/atoms/Input";
import { useCallback, useState, useEffect } from "react";
import { useSnackbar } from "notistack";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import * as Yup from "yup";
import TwoFactorAuthDialog, { Accounts } from "./TwoFactorAuthDialog";
import { login, createCredentialViaCode, checkLogin } from "@/lib/api";
import Button from "@/ui/atoms/Button";
import { getConfig } from "@/utils/server-config";

interface ISignInValues {
  email: string;
  password: string;
  token?: string;
}

export default function LoginForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [accounts, setAccounts] = useState<Accounts>();
  const [sessionId, setSessionId] = useState("");
  const [actualSignUpHref, setActualSignUpHref] = useState("/signup");
  const [defaultLandingPage, setDefaultLandingPage] = useState("");

  const { enqueueSnackbar } = useSnackbar();
  const { executeRecaptcha } = useGoogleReCaptcha();

  const validationSchema = Yup.object().shape({
    email: Yup.string()
      .matches(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        "Email is invalid"
      )
      .required("Email is required"),
    password: Yup.string().required("Password is required"),
  });

  const initialValues: ISignInValues = {
    email: "",
    password: "",
    token: "",
  };

  const {
    values,
    handleBlur,
    handleChange,
    errors,
    touched,
    handleSubmit,
    setFieldValue,
  } = useFormik({
    onSubmit: onSubmit,
    validationSchema: validationSchema,
    enableReinitialize: true,
    validateOnBlur: true,
    initialValues: initialValues,
  });

  let searchParams: URLSearchParams | undefined;
  if (typeof window !== "undefined") {
    searchParams = new URLSearchParams(window.location.search);
  }

  const onLoginSuccess = useCallback(
    async (code: string) => {
      // request callback qpi to set sessionId and token for 2FA login
      if (!code) {
        console.log("No code received", code);
      }

      try {
        const response = await createCredentialViaCode({ code });
        console.log("response", response);
        if (response.status?.code === 0) {
          enqueueSnackbar("Welcome back! You’re now logged in.");
          setTimeout(() => {
            const redirect =
              searchParams?.get("redirect") || defaultLandingPage;
            window.location.href = redirect;
          }, 1500);
        } else {
          enqueueSnackbar(response.status.msg || "Failed to login");
        }
      } catch (error: any) {
        console.log("Login error: ", error);
        enqueueSnackbar(error.message || "Failed to login");
      }
    },
    [enqueueSnackbar, searchParams, defaultLandingPage]
  );

  async function onSubmit(values: ISignInValues) {
    if (isLoading || !executeRecaptcha) return;
    try {
      const token = await executeRecaptcha("login");
      setIsLoading(true);
      const resp = await login({
        email: values.email,
        password: values.password,
        token: token,
      });
      if (resp.status?.code === 0) {
        onLoginSuccess(resp.data.code);
      } else if (resp.status?.code === 310020) {
        // 2FA required
        const { mfaAccounts, sessionId } = resp.data;
        setSessionId(sessionId);
        setAccounts(mfaAccounts);
        setDialogVisible(true);
      } else {
        enqueueSnackbar(resp.status.msg || "Failed to login");
      }
    } catch (error: any) {
      console.log("Login error: ", error);
      enqueueSnackbar(error.message || "Failed to login");
    } finally {
      setIsLoading(false);
    }
  }

  const handleClose = useCallback(() => {
    setDialogVisible(false);
  }, [setDialogVisible]);

  useEffect(() => {
    // This effect runs only on the client, after hydration
    const defaultLanding = getConfig(window.location.hostname)?.DEFAULT_LANDING_PAGE + "/admin/home";
    setDefaultLandingPage(defaultLanding);

    const currentSearchParamsOnClient = new URLSearchParams(
      window.location.search
    );
    const redirectParam = currentSearchParamsOnClient.get("redirect");
    if (redirectParam) {
      setActualSignUpHref(
        `/signup?redirect=${encodeURIComponent(redirectParam)}`
      );
    } else {
      setActualSignUpHref("/signup"); // Ensure it's the base path if no redirect
    }

    // Check if user is already logged in
    const checkLoginStatus = async () => {
      try {
        await checkLogin();
        // If checkLogin succeeds (HTTP 200), user is already logged in
        // Redirect to the appropriate page
        const redirectUrl = redirectParam || defaultLanding;
        window.location.href = redirectUrl;
      } catch (error: any) {
        // If checkLogin fails (HTTP 401/403), user is not logged in
        // Stay on login page - this is expected behavior
        console.log("User not logged in, staying on login page");
      }
    };

    checkLoginStatus();
  }, []); // Runs once on mount client-side

  // 包装handleChange，使email输入自动转小写
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<any>) => {
      setFieldValue("email", e.target.value.toLowerCase());
    },
    [setFieldValue]
  );

  return (
    <form
      onSubmit={handleSubmit}
      className="flex justify-center items-center h-full"
      data-testid="login-form-root"
      autoComplete="off"
    >
      <div
        className="rounded-lg w-[420px] max-w-[calc(100%-40px)]"
        data-testid="login-form-content"
      >
        <h1
          className={`mb-8 text-2xl font-bold select-none`}
          data-testid="login-form-title"
        >
          Login
        </h1>
        <div className="w-full flex flex-col gap-y-5">
          <Input
            label="Email"
            id="email"
            type="email"
            value={values.email}
            onChange={handleInputChange}
            onBlur={handleBlur}
            disabled={false}
            placeholder="<EMAIL>"
            error={!!(errors.email && touched.email)}
            helperText={errors.email && touched.email ? errors.email : ""}
            inputProps={{
              "data-testid": "login-form-email",
              autoComplete: "off",
            }}
          />
          <Input
            id="password"
            label="Password"
            type="password"
            placeholder="Enter Password"
            name="password"
            onBlur={handleBlur}
            onChange={handleChange}
            error={touched.password && Boolean(errors.password)}
            helperText={
              touched.password && errors.password ? errors.password : " "
            }
            inputProps={{
              "data-testid": "login-form-password",
              autoComplete: "new-password",
            }}
          />
        </div>
        <div className="flex justify-between items-center mb-[20px] h-[20px]">
          <a
            href="/reset-password"
            className="cursor-pointer text-sm font-medium text-primary-main"
            data-testid="login-form-forgot"
          >
            Forgot Password
          </a>
        </div>
        <Button
          className="w-full h-[40px] font-normal"
          variant="contained"
          type="submit"
          disabled={isLoading}
          loading={isLoading}
          data-testid="login-form-submit"
        >
          Log in
        </Button>
        <div className="flex justify-center"></div>
        <div className="mt-8 flex justify-center text-sm items-center">
          {"Don't have an account? "}
          <a
            href={actualSignUpHref}
            className="ml-1 cursor-pointer text-sm text-primary-main"
            data-testid="login-form-signup"
          >
            Sign Up Now!
          </a>
        </div>
      </div>
      <TwoFactorAuthDialog
        sessionId={sessionId}
        accounts={accounts}
        open={dialogVisible}
        onClose={handleClose}
        onSuccess={onLoginSuccess}
      />
    </form>
  );
}
