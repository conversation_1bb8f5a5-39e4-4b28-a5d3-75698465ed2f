"use client";

import { useState } from "react";
import SubmitResetPasswordFrom from "@/ui/auth/submit-reset-password-form";
import ConfirmResetPasswordForm from "@/ui/auth/confirm-reset-password-form";
import ResetPasswordResult from "@/ui/auth/ResetPasswordResult";

import { ResetPasswordFormStep } from "./types";

export default function SubmitResetPassword() {
  const [email, setEmail] = useState("");
  const [step, setStep] = useState(0);

  return (
    <div className="flex relative justify-center w-full h-full">
      {step === ResetPasswordFormStep.EMAIL_FORM ? (
        <ConfirmResetPasswordForm
          setConfirmEmail={setStep}
          setEmail={setEmail}
        />
      ) : null}

      {step === ResetPasswordFormStep.RESET_FORM ? (
        <SubmitResetPasswordFrom
          email={email}
          setConfirmEmail={setStep}
        />
      ) : null}
      {step === ResetPasswordFormStep.RESET_RESULT ? (
        <ResetPasswordResult />
      ) : null}
    </div>
  );
}
