"use client";

import { ReactNode } from "react";
import Image from "next/image";
import { Typography } from "@mui/material";
import imageSvg from "@/ui/svg/image.svg?url";
import LoftyWorksLogo from "@/ui/svg/LoftyWorks.svg?url";
import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";

type SignUpLayoutProps = {
  children: ReactNode;
};

export default function SignUpLayout({ children }: SignUpLayoutProps) {
  return (
    <GoogleReCaptchaProvider reCaptchaKey="6LdJgo4jAAAAAHYoowU4gkLZFcbXUZxTRF0FAXg5">
      <div
        className="flex flex-col md:flex-row min-h-min"
        data-testid="auth-layout-root"
      >
        <div className="flex-1" data-testid="auth-layout-left">
          <div
            className={`relative h-auto md:h-screen md:min-h-screen`}
            data-testid="auth-layout-bg-wrapper"
          >
            <div
              className={`absolute inset-0 bg-[url('https://cdn.lofty.com/image/fs/servicetool/202565/8/original_ca0d22a0527d4066.png')] bg-cover bg-center -z-10`}
              data-testid="auth-layout-bg"
            />
            <div
              className={`p-[60px] h-[100%] min-h-[600px]`}
              data-testid="auth-layout-content"
            >
              <div className="flex gap-x-8" data-testid="auth-layout-logo-row">
                <Image
                  className="h-[30px]"
                  src={LoftyWorksLogo}
                  alt="The LoftyWorks logo"
                  width={134}
                  height={30}
                  data-testid="auth-layout-loftyworks-img"
                />
                <Image
                  className="h-[30px]"
                  src={imageSvg}
                  alt="The Letting Partnership logo"
                  width={168}
                  height={30}
                  data-testid="auth-layout-logo-img"
                />
              </div>
              <div
                className="flex flex-col h-[85%] justify-center text-white gap-3"
                data-testid="auth-layout-desc"
              >
                <Typography
                  variant="h1"
                  component="h1"
                  className="text-white"
                  fontSize={24}
                  fontWeight={500}
                  data-testid="auth-layout-title"
                >
                  Grow your Business
                </Typography>
                <span
                  className="text-6xl leading-normal font-bold"
                  data-testid="auth-layout-slogan"
                >
                  One-stop-shop
                  <br />
                  for Estate Agents.
                </span>
                <span
                  className="font-normal text-3xl leading-normal tracking-tighter"
                  data-testid="auth-layout-summary"
                >
                  Award-winning compliance, AI-driven CRM, <br />
                  modern property management, and advanced <br />
                  client accounting. Built for today&apos;s agents.
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          className="flex-1 h-[100vh] py-[30px] overflow-y-auto flex flex-col items-center relative"
          data-testid="auth-layout-right"
        >
          {children}
        </div>
      </div>
    </GoogleReCaptchaProvider>
  );
}
