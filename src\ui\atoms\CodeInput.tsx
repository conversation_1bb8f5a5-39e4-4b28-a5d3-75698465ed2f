import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FocusEvent<PERSON>and<PERSON>,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";
import { Grid, TextField, Typography, Box } from "@mui/material";
import { twMerge } from "tailwind-merge";

interface CodeInputProps {
  name?: string;
  id?: string;
  value?: string;
  onBlur?: FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>;
  setIsReadyToConfirmCode?: Dispatch<SetStateAction<boolean>>;
  setFieldValue?: (name: string, value: unknown) => void;
  error?: string;
  disabled?: boolean;
  classes?: {
    root?: string;
    input?: string;
    errorContainer?: string;
    errorText?: string;
  };
  dataTestIdPrefix?: string;
}

export function CodeInput({
  name,
  id,
  value,
  setIsReadyToConfirmCode,
  setFieldValue,
  error,
  disabled,
  classes,
  dataTestIdPrefix,
  onBlur,
}: CodeInputProps) {
  const [code, setCode] = useState(
    value ? value?.split("") : ["", "", "", "", "", ""]
  );
  const inputsRef = useRef<Array<HTMLInputElement | null>>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleVerifyCodeChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number
  ) => {
    const value = e.target.value;
    const regex = /^[0-9]$/;

    if (value.length <= 1 && (regex.test(value) || value === "")) {
      setCode((prev) => {
        const newCode = [...prev];
        newCode[index] = value;

        return newCode;
      });

      if (index <= 5 && value !== "") {
        inputsRef.current[index + 1]?.focus();
      } else if (index <= 5 && value == "") {
        inputsRef.current[index - 1]?.focus();
      }
    }
  };

  useEffect(() => {
    setFieldValue && name && setFieldValue(name, code.join(""));

    if (code.length === 6 && !code.includes("")) {
      setIsReadyToConfirmCode && setIsReadyToConfirmCode(true);
    } else {
      setIsReadyToConfirmCode && setIsReadyToConfirmCode(false);
    }
  }, [code, name, setFieldValue, setIsReadyToConfirmCode]);

  useEffect(() => {
    const handler = (ev: ClipboardEvent) => {
      if (!containerRef.current) {
        return; 
      }
      const activeElement = document.activeElement;
      
      const isActiveInComponent = containerRef.current.contains(activeElement);      
      if (!isActiveInComponent) return;
      
      const data = ev?.clipboardData?.getData("text") || "";
      const digits = data.replace(/\D/g, "").slice(0, 6);
      if (digits.length === 6) {
        setCode(digits.split(""));
      }
    };

    document.addEventListener("paste", handler);
    return () => document.removeEventListener("paste", handler);
  }, []);

  return (
    <Box ref={containerRef}>
      <Grid container spacing={1.5} className={twMerge(classes?.root)}>
        {[...Array(6)].map((_, index) => (
          <Grid item flex={1} key={index}>
            <TextField
              inputRef={(el) => (inputsRef.current[index] = el)}
              name={`${name}_${index}`}
              id={`${id}_${index}`}
              onChange={(e) => {
                handleVerifyCodeChange(e, index);
              }}
              autoFocus={index === 0}
              value={code[index]}
              error={!!error}
              disabled={disabled}
              variant="outlined"
              fullWidth
              inputProps={{
                "data-testid": `${
                  dataTestIdPrefix || "verify_code_input"
                }_${index}`,
              }}
              className={twMerge(classes?.input)}
              sx={{
                "& .MuiInputBase-input": {
                  fontSize: "24px",
                  fontWeight: 400,
                  textAlign: "center",
                  height: "58px",
                  width: "58px",
                  bgcolor: "common.white",
                  padding: 0, // Ensure padding doesn't affect height/width
                  boxSizing: "border-box", // Ensure border/padding are included in height/width
                  // Responsive styles
                  "@media (max-width: lg)": {
                    height: "48px", // Adjusted for better proportion
                    fontSize: "20px",
                    width: "48px",
                  },
                },
              }}
              onBlur={onBlur}
            />
          </Grid>
        ))}
      </Grid>
      {!!error && (
        <Box
          className={twMerge(classes?.errorContainer)}
          sx={{
            mt: 4, // Default margin top
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Typography
            ml={0.5}
            variant="body1"
            color="error.main"
            className={twMerge(classes?.errorText)}
          >
            {error}
          </Typography>
        </Box>
      )}
    </Box>
  );
}
