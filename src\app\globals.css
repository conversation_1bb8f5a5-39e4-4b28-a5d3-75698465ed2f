@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #171717;
  }
}

@font-face {
  font-family: "SF-UI-Text-Bold";
  src: url("/fonts/SF-UI-Text-Bold.otf");
}

@font-face {
  font-family: "SF-UI-Text-Heavy";
  src: url("/fonts/SF-UI-Text-Heavy.otf");
}

@font-face {
  font-family: "SF-UI-Text-HeavyItalic";
  src: url("/fonts/SF-UI-Text-HeavyItalic.otf");
}

@font-face {
  font-family: "SF-UI-Text-Medium";
  src: url("/fonts/SF-UI-Text-Medium.otf");
}

@font-face {
  font-family: "SF-UI-Text-Regular";
  src: url("/fonts/SF-UI-Text-Regular.otf");
}

@font-face {
  font-family: "SF-UI-Text-RegularItalic";
  src: url("/fonts/SF-UI-Text-RegularItalic.otf");
}

@font-face {
  font-family: "SF-UI-Text-Semibold";
  src: url("/fonts/SF-UI-Text-Semibold.otf");
}

@font-face {
  font-family: "SF-UI-Text-SemiboldItalic";
  src: url("/fonts/SF-UI-Text-SemiboldItalic.otf");
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: "SF-UI-Text-Regular", -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
  margin: 0;
}

.fsform-container .fsBody {
  background-color: #f6f7fb;
  min-height: calc(100vh - 64px);
  font-family: "SF-UI-Text-Regular";
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #515666;
}

.fsform-container .fsForm {
  max-width: 700px;
}

.fsForm {
  border-color: #e1e2e6 !important;
  border-width: 1px !important;
  border-radius: 10px !important;
  font-family: "SF-UI-Text-Regular";
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #515666;
  padding: 20px 30px !important;
}

.fsForm .fsSectionHeader {
  min-height: 70px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-bottom: 20px;
  margin-top: -20px;
}

.fsForm .fsLabel,
.fsForm .fsOptionLabel {
  font-size: 14px !important;
  line-height: 20px !important;
}

.fsForm .fsLabel {
  color: #202437 !important;
  margin-bottom: 10px !important;
}

.fsForm .fsOptionLabel {
  color: #202437 !important;
  margin-right: 20px;
}

.fsForm .fsLabel .fsRequiredMarker {
  color: #ff285f !important;
}

.fsForm .fsSectionHeader {
  background-color: #fff !important;
  border-bottom: 1px solid #ebecf1;
  padding: 20px 0 !important;
  font-weight: 600;
  color: #202437 !important;
}

.fsForm .fsSection .fsFieldCell input[type="radio"],
.fsForm .fsSection .fsFieldCell input[type="radio"]:before {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  cursor: pointer;
}

.fsForm .fsSection .fsFieldCell input[type="radio"]:before {
  border-color: #fff;
  background-color: #fff;
  outline: 1px solid #c6c8d1 !important;
  margin-right: 10px;
}

.fsForm .fsSection .fsFieldCell input[type="radio"]:checked:before {
  background-color: #00acdb;
  border-color: #fff;
  outline: 1px solid #c6c8d1 !important;
}

.fsForm .fsSection .fsFieldCell input[type="checkbox"],
.fsForm .fsSection .fsFieldCell input[type="checkbox"]:before {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  cursor: pointer;
}

.fsForm .fsSection .fsFieldCell input[type="checkbox"]:before {
  border-color: #fff;
  border: 1px solid #c6c8d1;
}

.fsForm .fsSection .fsFieldCell input[type="checkbox"]:checked:before {
  color: #00acdb;
  content: "✓";
  text-align: center;
  font-size: 12px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 1px solid #c6c8d1;
}

.fsForm .fsSection .fsFieldCell input[type="text"],
.fsForm .fsSection .fsFieldCell select {
  height: 40px !important;
  border-radius: 6px !important;
  border-color: #c6c8d1 !important;
  padding: 0 10px !important;
  line-height: 20px !important;
  font-size: 14px !important;
  background: #fff !important;
  color: #202437 !important;
}

.fsForm .fsSection .fsFieldCell:not(.fsHiddenField) {
  display: flex;
  flex-direction: column;
}

/* .fsForm .fsSection .fsFieldCell select option {
  display: none;
} */

.mockSelect {
  display: none;
  position: absolute;
  background-color: #fff;
  border: 1px solid #e1e2e6;
  left: 0;
  right: 0;
  top: 100%;
  overflow: auto;
  max-height: 300px;
  padding: 5px;
  border-radius: 6px;
  z-index: 999;
  box-shadow: 0px 2px 5px 0px #0000001a;
}

.mockSelect.show {
  display: block;
}

.mockOption {
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  cursor: pointer;
  border-radius: 4px;
}

.mockOption.active {
  color: #00acdb;
}

.mockOption.active::after {
  content: "✓";
  justify-self: flex-end;
}

.mockOption:hover {
  color: #00acdb;
}

.fsForm .fsSection .fsFieldCell textarea {
  border-radius: 6px !important;
  border-color: #c6c8d1 !important;
  padding: 10px !important;
  line-height: 20px;
  font-size: 14px;
  background-color: #fff !important;
  color: #202437 !important;
}

.fsForm .fsSection .fsFieldCell input[type="text"]:focus,
.fsForm .fsSection .fsFieldCell select:focus {
  outline: none !important;
  border-color: #00acdb !important;
}

.fsForm .fsSection .fsFieldCell .fsValidationError {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  height: 40px !important;
  color: #ff285f !important;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  /* pointer-events: none; */
  margin-bottom: 5px !important;
  white-space: nowrap;
  order: 2;
}

.fsForm .fsSection .fsFieldCell .fsSubFieldGroup {
  order: 1;
}

/* 
.fsForm .fsSection .fsFieldCell:has(.fsDateField) .fsValidationError {
  position: static;
  white-space: nowrap;
  margin-bottom: 5px !important;
} */

.fsForm .fsSection .fsFieldCell .fsValidationError > div {
  color: #ff285f !important;
}

.fsForm .fsSection .fsFieldCell .fsValidationError + div input,
.fsForm .fsSection .fsFieldCell .fsValidationError + div select {
  border-color: #ff285f !important;
}

.fsSaveBtn {
  border-radius: 6px;
  background-color: #fff;
  color: #00acdb;
  width: 130px !important;
  height: 36px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fsSaveBtn:hover {
  border: 1px solid #00acdb;
}

.fsSaveBtn:active {
  border: 1px solid #00acdb;
  background-color: rgba(0, 172, 219, 0.1);
}

.fsForm .fsSubmit {
  border-top: 1px solid #ebecf1;
  height: 60px;
  box-sizing: border-box;
  padding-bottom: 0;
  margin-top: 0;
}

.fsForm .fsSubmit .fsNextButton,
.fsForm .fsSubmit .fsSubmitButton {
  border-radius: 6px !important;
  background-color: #00acdb !important;
  color: #fff !important;
  min-width: 130px !important;
  padding: 0 15px !important;
  height: 36px !important;
  font-size: 14px !important;
  line-height: 20px !important;
  border: none !important;
  outline: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
}

.fsForm .fsSubmit .fsPreviousButton {
  border-radius: 6px !important;
  background-color: #fff !important;
  color: #515666 !important;
  min-width: 130px !important;
  padding: 0 15px !important;
  height: 36px !important;
  font-size: 14px !important;
  line-height: 20px !important;
  border: 1px solid #e1e2e6 !important;
  outline: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
}

.fsForm .fsSubmit .fsPreviousButton:hover {
  background-color: #f9f9fa !important;
}

.fsForm .fsSubmit .fsPreviousButton:active {
  background-color: #eeeff1 !important;
}

.fsForm .fsSubmit .fsNextButton:hover,
.fsForm .fsSubmit .fsSubmitButton:hover {
  background-color: #19b4df !important;
}

.fsForm .fsSubmit .fsNextButton:active,
.fsForm .fsSubmit .fsSubmitButton:active {
  background-color: #009bc5 !important;
  outline: none !important;
}

.fsForm .fsFieldCell:has(input[type="file"]) .fsLabel + div {
  border: 1px dotted #00acdb;
  border-radius: 10px;
  background-color: rgba(0, 172, 219, 0.05);
  color: #00acdb;
  font-size: 12px;
  line-height: 20px;
  cursor: pointer;
  margin-bottom: 0;
}

.fsForm .fsFieldCell:has(input[type="file"]) .fsLabel + div > div {
  margin: 45px 0;
}

.fsForm .fsFieldCell:has(input[type="file"]) .fsLabel + div > div div {
  font-size: 14px;
}

.fsForm .fsFieldCell:has(input[type="file"]) .fsLabel + div + div {
  border-radius: 6px;
  background-color: #f6f7fb;
  color: #797e8b;
  font-size: 12px;
  line-height: 20px;
  cursor: pointer;
  margin-top: 10px;
  margin-bottom: 0;
}

.fsForm .fsFieldCell:has(input[type="file"]) .fsLabel + div svg {
  fill: #00acdb;
}

*:focus {
  outline: none !important;
}

*:focus:before {
  outline: none !important;
}

.agreement-checkbox .MuiFormControlLabel-asterisk {
  display: none;
}

.fsFieldCell ul {
  padding-left: 20px;
}

.fsFieldCell ul li::before {
  content: "*";
  margin-right: 4px;
}

.absolute-important {
  position: absolute !important;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

input[type="password"]::-ms-reveal {
  display: none;
}

input[type="password"]::-ms-clear {
  display: none;
}

.grecaptcha-badge {
  display: none;
}

.text-error {
  color: #ff285f !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0px 1000px #ffffff inset !important;
  -webkit-text-fill-color: #000000 !important;
  transition: background-color 5000s ease-in-out 0s;
}

input:autofill {
  box-shadow: 0 0 0px 1000px #ffffff inset !important;
  -webkit-text-fill-color: #000000 !important;
}

input:-moz-autofill {
  background-color: #ffffff !important;
  color: #000000 !important;
  box-shadow: none !important;
}